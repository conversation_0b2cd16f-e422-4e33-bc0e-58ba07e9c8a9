import React, {
  useContext,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react'
import type { ClassNames } from '../_utils/styleUtils'
import { HighlighterContext } from './HighlighterProvider'
import { useHighlighter } from './useHighlighter'

export interface PaletteProps {
  overrides?: {
    root?: ClassNames
  }
  /**
   * remote 开启时生效，优先级高于 code
   */
  url?: string
  code?: string
  lang: string
  /**
   * 是否远程加载
   */
  remote?: boolean
}

export interface PaletteRef {
  getContent: () => string | undefined
}

function InnerPalette(
  {
    overrides,
    code: codeProp,
    lang: langProp,
    url,
    remote = false,
  }: PaletteProps,
  ref: React.Ref<PaletteRef>,
) {
  const lang = langProp.toLowerCase()

  const topHighlighter = useContext(HighlighterContext)

  const highlighter = useHighlighter(topHighlighter)

  const [rawCode, setRawCode] = useState(remote ? undefined : codeProp)

  const [highlightedHtml, setHighlightedHtml] = useState('')

  useEffect(() => {
    if (!(remote && url)) {
      return
    }

    fetch(url)
      .then(response => response.text())
      .then(setRawCode)
    // ignore remote
  }, [url])

  useEffect(() => {
    if (remote) {
      return
    }
    setRawCode(codeProp)
  }, [codeProp])

  useEffect(() => {
    const generateHighlightedHtml = async () => {
      if (!highlighter || !lang || !rawCode) {
        return (
          rawCode?.replace(
            /[\u00A0-\u9999<>\&]/g,
            i => `&#${i.charCodeAt(0)};`,
          ) || ''
        )
      }

      // const loadedLanguages = highlighter.getLoadedLanguages() || []
      // const hasLoadedLanguage = loadedLanguages.includes(lang)

      let code = rawCode

      let _lang = lang
      if (_lang === 'typescript') {
        _lang = 'tsx'
      }

      if (_lang === 'json') {
        try {
          const json = JSON.parse(code)
          code = JSON.stringify(json, null, 2)
        } catch (error) {
          // ignore
        }
      }

      return highlighter.codeToHtml(code, {
        lang: _lang,
        theme: 'one-light',
        colorReplacements: {
          '#fafafa': 'none',
        },
        // transformers: [
        //   {
        //     // line(node, line) {
        //     //   node.properties['data-line'] = line
        //     //   if (highlightedLines.includes(line))
        //     //     this.addClassToHast(node, 'highlighted-line')
        //     // },
        //   },
        // ],
      })
    }

    generateHighlightedHtml().then(newHtml => {
      setHighlightedHtml(newHtml)
    })
  }, [rawCode, lang, highlighter])

  useImperativeHandle(ref, () => ({
    getContent: () => {
      return rawCode
    },
  }))

  return (
    <div
      className={overrides?.root}
      dangerouslySetInnerHTML={{ __html: highlightedHtml }}
    />
  )
}

export const Palette = React.forwardRef(InnerPalette)
