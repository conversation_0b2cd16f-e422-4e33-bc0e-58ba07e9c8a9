.markdown-rich-editor {
  --pre-color: #E1E1E5;
  --blockquote-color: #E1E1E5;

  line-height: 24px;
  outline: none;
  position: relative;

  * {
    line-height: 1.5em;
  }

  .ProseMirror {
    outline: none;
    overflow: hidden;
  }

  .ProseMirror-menubar {
    position: absolute;
    top: 0;
    right: 100%;
    width: 60px;
    z-index: 1;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 500;
    margin: 1em 0 0.5em;
  }

  h1 {
    font-size: 1.5em;
  }

  h2 {
    font-size: 1.25em;
  }

  h3 {
    font-size: 1.17em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.83em;
  }

  h6 {
    font-size: 0.67em;
  }

  ul {
    list-style-type: disc;
    padding-left: 1.5em;
  }

  ol {
    list-style-type: decimal;
    padding-left: 1.5em;
  }

  pre {
    background-color: var(--pre-color);
    border-radius: 4px;
    padding: 0.5em;
    overflow-x: auto;
  }

  blockquote {
    margin: 0;
    padding: 0 1em;
    border-left: .25em solid var(--blockquote-color);
  }

  p,
  pre,
  blockquote {
    margin-bottom: .5em;
  }
}