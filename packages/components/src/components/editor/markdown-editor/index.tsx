import './index.css'
import 'prosemirror-view/style/prosemirror.css'

import type { MutableRefObject } from 'react'
import {
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useMemoizedFn } from 'ahooks'
import { EditorState, Plugin } from 'prosemirror-state'
import { EditorView } from 'prosemirror-view'
import { history } from 'prosemirror-history'
import {
  schema,
  defaultMarkdownParser,
  defaultMarkdownSerializer,
} from 'prosemirror-markdown'
import { keymap } from 'prosemirror-keymap'
import { baseKeymap } from 'prosemirror-commands'
import { gapCursor } from 'prosemirror-gapcursor'
import classNames from 'classnames'
import { buildInputRules } from './inputrules'
import { buildKeymap } from './keymap'
import { MenuBar } from './menu'
import { getSelectedBlockNode, isInList } from './util'

export interface IMERef {
  view?: MutableRefObject<EditorView>
  focus: () => void
  changeValue: (value: string) => void
}

interface MarkdownEditorProps {
  eRef?: MutableRefObject<IMERef | undefined>
  className?: string
  placeholder?: string
  typeIcon?: Record<string, any>
  onChange?: (value: string) => void
}

export const MarkdownEditor = memo((props: MarkdownEditorProps) => {
  const { eRef, className, placeholder, typeIcon, onChange } = props

  const ref = useRef<HTMLDivElement>(null)
  const editRect = useRef<DOMRect>()
  const editorView = useRef<EditorView>()
  const lockedRef = useRef(false)
  const isCompositionRef = useRef(false)
  const [isEmpty, setIsEmpty] = useState(true)
  const [show, setShow] = useState(false)
  const [top, setTop] = useState(-1)
  const [inList, setInList] = useState(false)
  const [nowType, setNowType] = useState('paragraph')

  const valuePlugin = useMemo(() => {
    return new Plugin({
      view() {
        return {
          update(view, prevState) {
            const text = defaultMarkdownSerializer.serialize(view.state.doc)
            setIsEmpty(text.trim().length === 0)
            if (lockedRef.current || isCompositionRef.current) return
            if (view.state.doc === prevState.doc || !onChange) return
            onChange(text)
          },
        }
      },
    })
  }, [])

  const positionPlugin = useMemo(() => {
    return new Plugin({
      view() {
        return {
          update(view) {
            const block = getSelectedBlockNode(view.state)
            const inList = isInList(view.state)
            setInList(inList)
            if (['paragraph', 'heading'].includes(block.node.type.name)) {
              const coords = editorView.current.coordsAtPos(block.pos)
              setTop(coords.top - editRect.current.top)
              if (block.node.type.name !== 'heading') {
                setNowType(block.node.type.name)
              } else {
                setNowType(`head${block.node.attrs.level}`)
              }
            }
          },
        }
      },
    })
  }, [])

  const initEditor = useMemoizedFn(() => {
    editorView.current?.destroy()

    editRect.current = ref.current?.getBoundingClientRect()

    const view = new EditorView(ref.current, {
      state: EditorState.create({
        schema,
        doc: defaultMarkdownParser.parse(''),
        plugins: [
          buildInputRules(schema),
          keymap(buildKeymap(schema)),
          keymap(baseKeymap),
          gapCursor(),
          history(),
          valuePlugin,
          positionPlugin,
        ],
      }),
      handlePaste(view, event) {
        const plainText = event.clipboardData.getData('text/plain')
        if (plainText) {
          event.preventDefault()
          const { tr, selection } = view.state
          const { from } = selection
          const node = defaultMarkdownParser.parse(plainText)
          const insertTr = tr.deleteSelection().insert(from, node.content)
          view.dispatch(insertTr)
        }
        return true
      },
    })

    view.dom.addEventListener('focus', () => {
      setShow(true)
    })

    view.dom.addEventListener('blur', () => {
      setShow(false)
    })

    view.dom.addEventListener('compositionstart', () => {
      isCompositionRef.current = true
    })

    view.dom.addEventListener('compositionend', () => {
      isCompositionRef.current = false
      const text = defaultMarkdownSerializer.serialize(view.state.doc)
      onChange(text)
    })

    editorView.current = view

    return view
  })

  const changeValue = useMemoizedFn((value: string) => {
    lockedRef.current = true
    editorView.current?.dispatch(
      editorView.current.state.tr.replaceWith(
        0,
        editorView.current.state.doc.content.size,
        defaultMarkdownParser.parse(value),
      ),
    )

    setTimeout(() => {
      lockedRef.current = false
    }, 1000)
  })

  useEffect(() => {
    initEditor()
    return () => {
      editorView.current?.destroy()
    }
  }, [])

  useImperativeHandle(eRef, () => ({
    view: editorView,
    focus: () => {
      if (editorView.current.hasFocus()) return
      editorView.current?.focus()
    },
    changeValue,
  }))

  return (
    <div className={classNames('markdown-rich-editor relative', className)}>
      <div ref={ref} />
      {isEmpty && (
        <span className='absolute left-0 top-0 c-#8D8D99/80 pointer-events-none select-none'>
          {placeholder ?? ''}
        </span>
      )}
      <div
        className='absolute mr-10px right-100% top-0 duration-300'
        style={{ top: `${top}px`, opacity: show ? 1 : 0 }}
      >
        <MenuBar
          typeIcon={typeIcon}
          nowType={nowType}
          inList={inList}
          editorView={editorView}
        />
      </div>
    </div>
  )
})
