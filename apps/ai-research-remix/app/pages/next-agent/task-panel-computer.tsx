import React, { useCallback } from 'react'
import {
  EventStatus,
  EventType,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type {
  BrowserUseOutput,
  CommonSearchOutput,
  Event,
  FileOperatorOutput,
  FileOperatorInput,
  ImageSearchOutput,
} from '@apis/mindnote/next-agent-chat.type'
import { isString } from 'lodash-es'
import { tryParseToJsonObject } from '@bty/util'
import { Palette } from '@bty/components'
import NiceModal from '@ebay/nice-modal-react'
import { IconButton } from '@/components/base/icon'
import { useTaskStore } from '@/store/task'
import { useTaskListStore } from '@/store/task-list'
import { useEventInProgress, useEventSnapshot } from '@/store/task-event'
import { TaskTimeline } from './task/task-timeline'
import { TaskSearch } from './task/task-search'
import { TaskComputerUse } from './task/task-computer-use'
import { TaskTerminal } from './task/task-terminal'
import { TaskMarkdown } from './task/task-markdown'
import { TaskLoading } from './task/task-loading'
import { TaskFile } from './task/task-file'
import { supportedLangs, TaskCode } from './task/task-code'
import { TaskMcp } from './task/task-mcp'
import type { ActionType } from './utils'
import { Action, getActionIcon, processMcpToolName } from './utils'
import { TaskHtml } from './task/task-html'
import { TaskComputerUseModal } from './task/task-computer-use-modal'
import { TaskImage } from './task/task-image'

function resolveFileComponent(props: {
  ext: string
  path?: string
  content?: string
}) {
  if (props.ext === 'md') {
    return <TaskMarkdown key={props.path} {...props} />
  }
  if (props.ext === 'html') {
    return <TaskHtml key={props.path} {...props} />
  }
  if (supportedLangs.includes(props.ext)) {
    return <TaskCode key={props.path} {...props} lang={props.ext} />
  }
  // 其他文件类型 (csv, pdf, etc)
  return <TaskFile key={props.path} {...props} type={props.ext} />
}

export const TaskPanelComputer = React.memo(() => {
  const taskId = useTaskListStore(s => s.taskId)

  const event = useEventInProgress()

  const hidePanel = useTaskStore(state => state.hidePanel)

  const { snapshot, allSnapshots, setSnapshot } = useEventSnapshot()

  const handleTimelineChange = useCallback((event?: Event) => {
    setSnapshot(event?.event_id || undefined)
  }, [])

  if (!allSnapshots?.length) {
    return
  }

  const resolvedEvent = snapshot || event

  // TODO: action_type 以及mcp逻辑等待后续处理 先暂时使用该逻辑处理
  const isMcp =
    resolvedEvent?.event_type === EventType.TOOL_CALL &&
    (resolvedEvent?.content as any)?.action_type === ToolType.MCP

  // action_type后续需要改造
  const label = isMcp ? '正在调用MCP' : resolvedEvent?.content.action_name

  const args = (() => {
    if (isMcp) {
      const toolName = processMcpToolName(
        resolvedEvent?.content?.metadata?.tool_name,
      )
      return `${resolvedEvent?.content?.metadata?.name}/${toolName}`
    }
    return resolvedEvent?.content.arguments?.join(' ')
  })()

  const resolveComponentAndActions = () => {
    if (!resolvedEvent) {
      return [<TaskLoading key='loading' />]
    }

    const { content, event_status } = resolvedEvent
    const tool = content.tool_name
    const output = content.tool_output

    if (isMcp) {
      return [
        <TaskMcp
          key='task-mcp'
          mcpInfo={{
            description: content.metadata?.description,
            tool_input: content.tool_input,
            tool_output: output,
          }}
        />,
      ]
    }

    const isCallFlow =
      resolvedEvent.event_type === EventType.TOOL_CALL &&
      (content as any).action_type === ToolType.CallFlow

    if (isCallFlow) {
      return [<TaskMarkdown key='task-call-flow' content={output as string} />]
    }

    switch (tool) {
      case ToolType.WebSearch:
      case ToolType.LocalSearch:
        return [
          <TaskSearch
            key='task-search'
            mode={tool}
            value={output as CommonSearchOutput}
          />,
        ]
      case ToolType.ImageSearch:
        return [
          <TaskImage key='task-image' value={output as ImageSearchOutput} />,
        ]
      case ToolType.BrowserUse: {
        const useVnc = !snapshot
        const _snapshot = useVnc
          ? undefined
          : event_status === EventStatus.SUCCESS && output
            ? (output as BrowserUseOutput)
            : undefined
        return [
          <TaskComputerUse
            key='task-computer-use'
            snapshot={_snapshot}
            useVnc={useVnc}
          />,
          [
            [
              Action.Fullscreen,
              () =>
                NiceModal.show(TaskComputerUseModal, { owner: 'ai', taskId }),
            ],
          ],
        ]
      }
      case ToolType.TerminalOperator: {
        const command = Array.isArray(output)
          ? output.join('\n')
          : isString(output)
            ? output
            : ''
        return [<TaskTerminal key='task-terminal' command={command} />]
      }
      case ToolType.WebFetch:
        return [<TaskMarkdown key='task-file' content={output as string} />]
      case ToolType.ReadFile: {
        const _output = output as Required<FileOperatorOutput>
        if (!_output) {
          return [<TaskLoading key='loading' />]
        }
        return [
          resolveFileComponent({
            ext: _output.file_type,
            path: _output.path,
          }),
        ]
      }
      case ToolType.CreateFile:
      case ToolType.StrReplace:
      case ToolType.WriteFile: {
        const input = content.tool_input
          ? (tryParseToJsonObject(content.tool_input) as FileOperatorInput)
          : undefined
        if (!input) {
          return [<TaskLoading key='loading' />]
        }
        const ext = input.file_type || input.file_path?.split('.').pop() || ''
        const text = input.file_content || input.new_str
        return [resolveFileComponent({ ext, content: text })]
      }
      default:
        return [<TaskLoading key='loading' />]
    }
  }

  const [component, actions] = resolveComponentAndActions() as [
    JSX.Element,
    [ActionType, VoidFunction][],
  ]

  return (
    <div className='h-full flex flex-col relative'>
      <div className='shrink-0 text-14px h-72px px-24px flex items-center relative'>
        <img
          className='absolute top-12px w-72px h-72px object-contain'
          src={resolvedEvent?.agent_info?.icon}
        />
        <span className='shrink-0 text-font ml-88px mr-6px'>{label}</span>
        <span className='text-#9e9e9e truncate'>{args}</span>
        <div className='shrink-0 ml-auto h-32px flex items-center gap-8px'>
          {actions?.map(([action, onClick]) => (
            <IconButton
              key={action}
              size='size-32px'
              icon={getActionIcon(action)}
              iconSize='size-14px'
              onClick={onClick}
            />
          ))}
          <IconButton
            size='size-32px'
            icon='i-icons-collapse'
            iconSize='size-14px'
            onClick={hidePanel}
          />
        </div>
      </div>
      <div
        className='flex-1 px-24px pt-24px pb-96px overflow-auto border-t border-solid border-white rounded-t-24px'
        style={{
          background:
            'linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, #FFFFFF 100%)',
        }}
      >
        <Palette.Provider>{component}</Palette.Provider>
      </div>
      <TaskTimeline
        className='h-48px px-12px absolute inset-x-24px bottom-24px bg-white border border-solid border-[rgba(225,225,229,0.8)] shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] rounded-12px'
        eventId={resolvedEvent?.event_id}
        events={allSnapshots}
        onChange={handleTimelineChange}
      />
    </div>
  )
})
