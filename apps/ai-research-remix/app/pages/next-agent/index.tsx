import './index.scss'

import { memo, useEffect, useMemo, useRef, useState, type FC } from 'react'
import { useSize } from 'ahooks'
import { useTaskListStore } from '@/store/task-list'
import { useTeamTheme } from '@/store/team'
import { ChatHeader } from './header'
import { TaskList } from './task-list'
import { NextAgentMessageInput } from './message-input'
import { NextAgentMessageList } from './message-list'
import { useNextAgent } from './provider/NextAgentProvider'

// 解析CSS渐变的工具函数
function parseGradientBackground(cssBackground: string) {
  // 移除最后的逗号和基础背景色
  const parts = cssBackground.split(',')
  const baseColor = parts[parts.length - 1]?.trim() || '#FFFFFF'

  // 提取渐变部分
  const gradients = parts.slice(0, -1).map(part => part.trim())

  return {
    gradients,
    baseColor,
  }
}

// 创建mask样式的工具函数
function createMaskStyle(
  chatSize: { width: number; height: number } | null,
  cssBackground: string,
) {
  if (!chatSize) return null

  const { gradients, baseColor } = parseGradientBackground(cssBackground)
  const maskWidth = chatSize.width + 48
  const maskHeight = chatSize.height

  return {
    backgroundImage: gradients.join(', '),
    backgroundColor: baseColor,
    backgroundSize: `${maskWidth}px ${maskHeight}px`,
    backgroundPosition: '0px -45px',
    backgroundRepeat: 'no-repeat',
  }
}

export interface AskChatProps {
  renders?: {
    header?: FC | boolean
    messageList?: FC | boolean
  }
  isShareMode?: boolean
}

export const AskChat = memo<AskChatProps>(() => {
  const { isShareMode, teamId, messageList } = useNextAgent()
  const chatRef = useRef<HTMLDivElement>(null)
  const chatSize = useSize(chatRef)

  const [selectTeamId, setSelectTeamId] = useState('')

  const targetTeamId = useMemo(() => {
    if (messageList.length > 0) {
      return teamId
    }
    return selectTeamId
  }, [selectTeamId, teamId, messageList.length])

  const teamTheme = useTeamTheme(targetTeamId)

  useEffect(() => {
    if (isShareMode) {
      return
    }
    useTaskListStore.getState().init()

    return () => {
      useTaskListStore.getState().clearPolling()
    }
  }, [isShareMode])

  const mask = useMemo(() => {
    const background = teamTheme.starter
    const maskStyle = createMaskStyle(chatSize || null, background)

    if (!maskStyle) return null

    return (
      <div
        className='sticky-header-mask left-[-24px]! right-[-24px] h-45px absolute top-0 z-1'
        style={maskStyle}
      ></div>
    )
  }, [chatSize, teamTheme])

  return (
    <div className='next-agent-chat flex-1 min-w-0 h-full flex of-hidden relative!'>
      {!isShareMode && <TaskList />}

      <div
        ref={chatRef}
        className='chat flex-1 min-w-0 size-full flex flex-col of-hidden rd-24px'
        style={{
          background: teamTheme.starter,
        }}
      >
        <ChatHeader />
        <div className='flex-1 size-full flex of-hidden'>
          <div className='chat-container pb-24px px-24px flex-1 h-full flex flex-col justify-center items-center overflow-hidden'>
            <NextAgentMessageList mask={mask} />
            <div className='w-full max-w-960px min-w-200px shrink-0'>
              <NextAgentMessageInput
                selectTeamId={selectTeamId}
                handleTeamChange={setSelectTeamId}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})
