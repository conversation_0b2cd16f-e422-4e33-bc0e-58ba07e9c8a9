import React, { useState, useCallback, useMemo } from 'react'
import {
  Button,
  message,
  Form,
  Modal,
  TimePicker,
  DatePicker,
  Input,
  Spin,
} from 'antd'
import NiceModal from '@ebay/nice-modal-react'
import dayjs from 'dayjs'
import classNames from 'classnames'
import { useRequest } from 'ahooks'
import {
  getAgentTeamDetail,
  getAutomationTaskDetail,
} from '@apis/mindnote/next-agent-chat'
import type {
  AgentInfo,
  TaskPlan as TaskPlanType,
  TeamDetailResponse,
} from '@apis/mindnote/next-agent-chat.type'
import { EventType, ToolType } from '@apis/mindnote/next-agent-chat.type'
import { omit } from 'lodash-es'
import { IconButton } from '../../components/base/icon'
import { Select } from '@/components/base/select'
import { ScrollView } from '@/components/base/scroll-view'
import { useTaskPanel } from '@/store/task'
import { useTaskListStore } from '@/store/task-list'
import { useLatestTaskEvent, useTaskEventList } from '@/store/task-event'
import { useNextAgent } from './provider/NextAgentProvider'
import TaskMarkdownEditor from './components/markdown-editor'
import type { PlanConfigProps } from './components/message-item/plan'

// 表单中使用的 TaskStep 类型
interface FormTaskStep {
  id: string
  content?: string
  title?: string
  // JSON 格式的步骤数据
  'step-title'?: string
  'step-list'?: string[]
  step_id?: string
  step_number?: number
  step_name?: string
  step_detail?: string
  agent_name?: string
  agent_id: string
}

// 表单数据类型
interface FormTaskPlan {
  title?: string
  team?: string
  execution_plan?: {
    schedule?: {
      time?: string
      frequency?: string // 直接使用 "daily", "weekly", "monthly", "once"
      day_of_week?: number | null
      day_of_month?: number | null
      // 新增：用于"不重复"的具体日期
      execution_date?: string | null
    }
  }
  steps: FormTaskStep[]
  team_id?: string
  definition_id?: string
  name?: string
  disabled?: boolean
}

// JSON 输入输出接口
interface TaskPlanProps {
  taskType: 'schedule' | 'task'
  // 数据变化回调
  onChange?: (data: FormTaskPlan) => void
  // 关闭回调
  onClose?: () => void
  // 开始运行回调
  onStart?: (data: TaskPlanType) => void
  onDelete?: () => void
  config?: PlanConfigProps['planConfig']
  agentTeamList?: TeamDetailResponse['sub_agents']
  teamInfo?: Omit<TeamDetailResponse, 'sub_agents'>
  hiddenHeader?: boolean
  showExtraFooter?: boolean
  disabled?: boolean
  className?: string
}

// 获取当前时间的默认值
function getCurrentDefaults() {
  const now = dayjs()
  return {
    currentDate: now.format('YYYY-MM-DD'),
    tomorrowDate: now.add(1, 'day').format('YYYY-MM-DD'),
    currentWeekday: now.day(), // 直接返回数字 0-6
    currentDayOfMonth: now.date(),
    currentTime: now.format('HH:mm'),
    defaultTime: '09:00',
  }
}

// 移除复杂的映射，直接使用简单的频率选项
const FREQUENCY_OPTIONS = [
  { value: 'once', label: '不重复' },
  { value: 'daily', label: '每天' },
  { value: 'weekly', label: '每周' },
  { value: 'monthly', label: '每月' },
]

// 星期选项 (0-6: 周日到周六)
const WEEKDAY_OPTIONS = [
  { value: 0, label: '周日' },
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
]

// 月份选项（用于辅助选择日期）
const MONTH_OPTIONS = [
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' },
]

// 默认数据
const defaultTaskData: FormTaskPlan = {
  steps: [],
  execution_plan: {
    schedule: {
      frequency: 'daily',
      time: '09:00',
      day_of_week: null,
      day_of_month: null,
      execution_date: null,
    },
  },
}

// 从 HTML 内容中提取标题
function extractTitleFromContent(htmlContent: string): string {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')
  const h1 = doc.querySelector('h1')
  return h1?.textContent?.trim() || ''
}

// 将 HTML 内容转换为 JSON 格式（序列化）
function convertHtmlToJson(htmlContent: string): {
  'step-title': string
  'step-list': string[]
} {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')

  // 提取 h1 标题
  const h1 = doc.querySelector('h1')
  const stepTitle = h1?.textContent?.trim() || ''

  // 提取 ul 列表项
  const listItems = doc.querySelectorAll('li')
  const stepList = Array.from(listItems)
    .map(li => {
      // 获取 li 内的 p 标签文本，如果没有 p 标签则直接获取 li 的文本
      const p = li.querySelector('p')
      return (p?.textContent || li.textContent)?.trim() || ''
    })
    .filter(text => text.length > 0) // 过滤掉空项

  return {
    'step-title': stepTitle,
    'step-list': stepList,
  }
}

// 将 JSON 格式转换为 HTML 内容（反序列化）
function convertJsonToHtml(jsonData: {
  'step-title': string
  'step-list': string[]
}): string {
  const { 'step-title': stepTitle, 'step-list': stepList } = jsonData

  // 构建 HTML 结构
  const title = stepTitle || ''
  const listItems =
    stepList && stepList.length > 0
      ? stepList.map(item => `<li><p>${item}</p></li>`).join('')
      : '<li><p></p></li>' // 至少要有一个空的列表项

  return `<h1>${title}</h1><ul>${listItems}</ul>`
}

// 验证和转换输入数据
function validateAndTransformData(
  config?: PlanConfigProps['planConfig'],
): FormTaskPlan {
  // 如果有config数据，使用config
  if (config) {
    try {
      const configData =
        typeof config === 'string' ? JSON.parse(config) : config
      configData.steps = _convertFromPlanFormat(configData.steps || [])
      return configData
    } catch (error) {
      console.error('解析config数据失败:', error)
    }
  }

  // 如果没有config或解析失败，返回默认数据
  return { ...defaultTaskData }
}

// 从plan.tsx格式转换为task-plan格式
function _convertFromPlanFormat(planData: any[]): FormTaskStep[] {
  return planData.map(item => ({
    id: item.step_id || item.id,
    content: item.step_detail
      ? convertJsonToHtml({
          'step-title': item.step_name || item.title,
          'step-list':
            typeof item.step_detail === 'string'
              ? JSON.parse(item.step_detail)
              : item.step_detail,
        })
      : '<h1></h1><ul><li><p></p></li></ul>',
    assignee: item.agent_name || item.agent_id,
    agent_id: item.agent_id,
    title: item.step_name || item.title,
    step_id: item.step_id,
    step_number: item.step_number,
    step_name: item.step_name,
    step_detail: item.step_detail,
    agent_name: item.agent_name,
  }))
}

function generateOutput(
  data: FormTaskPlan,
  taskType: 'schedule' | 'task' = 'task',
): TaskPlanType {
  const isScheduleTask = taskType === 'schedule'

  const outputData: TaskPlanType = {
    steps: data.steps.map((step, index) => {
      const jsonContent = convertHtmlToJson(step.content || '')
      return {
        step_id: step.step_id || step.id,
        step_name:
          jsonContent['step-title'] ||
          step.step_name ||
          extractTitleFromContent(step.content || ''),
        step_detail: JSON.stringify(jsonContent['step-list']),
        step_number: index + 1,
        agent_name: step.agent_name || null,
        agent_id: step.agent_id,
        step_experience: null,
        sub_steps: [],
        content: step.content,
      }
    }),
    team_id: data.team_id,
    definition_id: isScheduleTask ? data.definition_id : undefined,
    name: data.title || data.name,
    execution_plan:
      isScheduleTask && data.execution_plan?.schedule
        ? {
            schedule: {
              time: data.execution_plan.schedule.time || '09:00',
              frequency: data.execution_plan.schedule.frequency || 'daily',
              day_of_week: data.execution_plan.schedule.day_of_week,
              day_of_month: data.execution_plan.schedule.day_of_month,
              execution_date: data.execution_plan.schedule.execution_date,
              // 不包含 month 字段，因为它只是用于辅助选择
            },
          }
        : {},
  }

  return outputData
}

// 根据频率设置默认值的辅助函数
function setDefaultsByFrequency(
  frequency: string,
  defaults: ReturnType<typeof getCurrentDefaults>,
) {
  const scheduleDefaults: any = {
    frequency,
    time: defaults.defaultTime,
    day_of_week: null,
    day_of_month: null,
    execution_date: null,
  }

  if (frequency === 'weekly') {
    scheduleDefaults.day_of_week = defaults.currentWeekday
  } else if (frequency === 'monthly') {
    scheduleDefaults.day_of_month = defaults.currentDayOfMonth
  } else if (frequency === 'once') {
    scheduleDefaults.execution_date = defaults.tomorrowDate
  }

  return scheduleDefaults
}

const TaskPlan: React.FC<TaskPlanProps> = ({
  onChange,
  onClose,
  onStart,
  onDelete,
  config,
  agentTeamList,
  teamInfo,
  taskType = 'task',
  hiddenHeader = false,
  showExtraFooter = false,
  disabled = false,
  className = 'h-full flex flex-col',
}) => {
  const { hidePanel } = useTaskPanel()

  const [form] = Form.useForm<FormTaskPlan>()

  const isScheduleTask = taskType === 'schedule'

  // 处理团队选择的选项
  const agentOptions = useMemo(() => {
    // 根据 agentTeamList 生成选项
    return agentTeamList?.map(agent => ({
      value: agent.agent_id,
      label: (
        <div className='flex items-center space-x-8px'>
          <div className='w-20px h-20px rounded-full bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center text-10px text-white'>
            {agent.icon ? (
              <img
                src={agent.icon}
                alt={agent.agent_name}
                className='w-full h-full rounded-full object-cover'
              />
            ) : (
              agent.agent_name.charAt(0).toUpperCase()
            )}
          </div>
          <div className='flex'>
            <span className='text-14px font-medium'>{agent.agent_name}</span>
            <span className='text-12px text-gray-500 ml-8px'>
              {agent.agent_expert_name}
            </span>
          </div>
        </div>
      ),
    }))
  }, [agentTeamList])

  // 监听外部数据变化
  useMemo(() => {
    if (config) {
      const newData = validateAndTransformData(config)
      form.setFieldsValue(newData)
    } else {
      // 设置初始默认值
      const defaults = getCurrentDefaults()
      const initialFrequency = 'daily'
      const scheduleDefaults = setDefaultsByFrequency(
        initialFrequency,
        defaults,
      )

      const defaultData = {
        ...defaultTaskData,
        ...(isScheduleTask && {
          execution_plan: {
            schedule: scheduleDefaults,
          },
        }),
      }
      form.setFieldsValue(defaultData)
    }
  }, [config, form, agentTeamList, taskType, isScheduleTask])

  // 表单值变化时的回调
  const handleFormChange = useCallback(
    (changedFields: any, allFields: FormTaskPlan) => {
      // 检查是否是频率变化 - 只有在 schedule 任务时才处理
      if (isScheduleTask) {
        // 检查是否是频率字段变化
        const frequencyChanged =
          changedFields.execution_plan?.schedule?.frequency !== undefined

        if (frequencyChanged) {
          const defaults = getCurrentDefaults()
          const newFrequency = allFields.execution_plan?.schedule?.frequency

          // 使用辅助函数设置默认值
          const scheduleDefaults = setDefaultsByFrequency(
            newFrequency || 'daily',
            defaults,
          )

          // 更新表单值
          form.setFieldsValue({
            execution_plan: {
              ...allFields.execution_plan,
              schedule: {
                ...allFields.execution_plan?.schedule,
                ...scheduleDefaults,
              },
            },
          })
        }
      }

      // 更新步骤标题
      const updatedData = {
        ...allFields,
        steps:
          allFields.steps?.map(step => ({
            ...step,
            title: extractTitleFromContent(step.content || ''),
          })) || [],
      }

      onChange?.(updatedData)
    },
    [onChange, form, isScheduleTask],
  )

  const [alwaysToBottom, setAlwaysToBottom] = useState(0)

  const addNewStep = useCallback(() => {
    const currentSteps = form.getFieldValue('steps') || []
    const newStep: FormTaskStep = {
      id: String(Date.now()), // 使用时间戳确保唯一性
      content: '<h1></h1><ul><li><p></p></li></ul>',
      agent_id: agentTeamList?.[0]?.agent_id!,
      title: '',
    }

    const newSteps = [...currentSteps, newStep]
    form.setFieldValue('steps', newSteps)

    setAlwaysToBottom(prev => prev + 1)

    // 触发表单变化
    const allValues = form.getFieldsValue()
    handleFormChange([], { ...allValues, steps: newSteps })
  }, [form, handleFormChange, agentTeamList])

  const removeStep = useCallback(
    (stepIndex: number) => {
      const currentSteps = form.getFieldValue('steps') || []

      if (currentSteps.length <= 1) {
        message.warning('至少需要保留一个任务步骤')
        return
      }

      const newSteps = currentSteps.filter(
        (_: any, index: number) => index !== stepIndex,
      )
      form.setFieldValue('steps', newSteps)

      // 触发表单变化
      const allValues = form.getFieldsValue()
      handleFormChange([], { ...allValues, steps: newSteps })
    },
    [form, handleFormChange],
  )

  const handleStart = useCallback(() => {
    form
      .validateFields()
      .then(async values => {
        try {
          if (
            values.steps?.some(
              (step: FormTaskPlan['steps'][number]) =>
                !step.content ||
                step.content === '<h1></h1><ul><li><p></p></li></ul>',
            )
          ) {
            message.error('请完善所有任务步骤的内容')
            return
          }

          console.log('开始运行任务:', values)
          console.log('输出 JSON:', generateOutput(values, taskType))
          await onStart?.(generateOutput(values, taskType))
        } catch (error) {
          console.error('启动任务失败:', error)
          message.error('启动任务失败，请检查配置')
        }
      })
      .catch(errorInfo => {
        console.error('表单验证失败:', errorInfo)
        message.error('请检查表单填写')
      })
  }, [form, onStart, taskType])

  const _handleClose = useCallback(() => {
    onClose?.()
  }, [onClose])

  const _handleDelete = useCallback(() => {
    onDelete?.()
  }, [onDelete])

  const scheduleFrequency = Form.useWatch(
    ['execution_plan', 'schedule', 'frequency'],
    form,
  )

  // 用于辅助选择日期的月份状态
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(
    dayjs().month() + 1, // 默认为当前月份
  )

  // 处理月份选择变化，校验当前日期是否有效
  const handleMonthChange = useCallback(
    (newMonth: number) => {
      setSelectedMonth(newMonth)

      // 获取当前选择的日期
      const currentDay = form.getFieldValue([
        'execution_plan',
        'schedule',
        'day_of_month',
      ])

      if (currentDay && newMonth) {
        // 检查新月份中是否存在当前选择的日期
        const daysInNewMonth = dayjs()
          .month(newMonth - 1)
          .daysInMonth()

        if (currentDay > daysInNewMonth) {
          // 如果当前日期超过了新月份的天数，清空日期选择
          form.setFieldValue(
            ['execution_plan', 'schedule', 'day_of_month'],
            undefined,
          )
          message.error('存在不符合规则的日期，已清空日期选择，请重新选择')
        }
      }
    },
    [form],
  )

  const monthDayOptions = useMemo(() => {
    const options = []
    if (!selectedMonth) {
      // 如果没有选择月份，提示用户先选择月份
      return [{ value: null, label: '请先选择月份', disabled: true }]
    }

    // 使用选择的月份来确定天数
    const daysInMonth = dayjs()
      .month(selectedMonth - 1)
      .daysInMonth()

    // 生成1到该月实际天数的选项
    for (let i = 1; i <= daysInMonth; i++) {
      options.push({ value: i, label: `${i}号` })
    }
    return options
  }, [selectedMonth])

  return (
    <div className={classNames('h-full flex flex-col', className)}>
      {!hiddenHeader && (
        <div className='h-60px text-16px leading-60px px-24px flex items-center justify-between'>
          修改任务方案
          <IconButton
            className='absolute top-24px right-24px z-10'
            size='size-32px'
            icon='i-icons-close'
            iconSize='size-16px'
            onClick={hidePanel}
          />
        </div>
      )}
      {/* Content */}
      <div className='flex-1 of-hidden'>
        <ScrollView toBottomId={alwaysToBottom}>
          <Form
            className={hiddenHeader ? '' : 'px-24px py-12px'}
            disabled={disabled}
            form={form}
            layout='vertical'
            onValuesChange={handleFormChange}
          >
            {/* Task Title */}
            {isScheduleTask && (
              <>
                {/* Team Selection */}
                <div className='flex items-center space-x-16px mb-24px'>
                  <p className='text-14px font-500 text-#17171D w-70px'>
                    团队
                    <span className='text-red-500 ml-4px'>*</span>
                  </p>
                  <Form.Item
                    name='team_id'
                    required={false}
                    rules={[{ required: false, message: '请选择团队' }]}
                    className='mb-0 flex-1'
                  >
                    <div className='px-12px py-10px h-36px flex items-center rounded-20px bg-[rgba(98,105,153,0.06)] inline-flex'>
                      <img
                        src={teamInfo?.icon}
                        alt={teamInfo?.team_name}
                        className='w-24px h-24px rounded-full object-cover'
                      />
                      <p className='ml-8px'>{teamInfo?.team_name}</p>
                    </div>
                  </Form.Item>
                </div>
              </>
            )}

            {/* Schedule */}
            {isScheduleTask && (
              <div className='mb-24px'>
                <div className='flex items-center space-x-16px mb-24px'>
                  <p className='text-14px font-500 text-#17171D w-70px'>
                    执行计划
                    <span className='text-red-500 ml-4px'>*</span>
                  </p>
                  <Form.Item
                    required={false}
                    name={['execution_plan', 'schedule', 'frequency']}
                    rules={[{ required: false, message: '请选择执行频率' }]}
                    className='mb-0'
                  >
                    <Select
                      variant='filled'
                      className='text-14px'
                      allowClear={false}
                      style={{ minWidth: 100 }}
                      options={FREQUENCY_OPTIONS}
                    />
                  </Form.Item>

                  {/* 周选择器 - 仅在每周时显示 */}
                  {scheduleFrequency === 'weekly' && (
                    <Form.Item
                      name={['execution_plan', 'schedule', 'day_of_week']}
                      required={false}
                      rules={[{ required: true, message: '请选择星期' }]}
                      className='mb-0'
                    >
                      <Select
                        variant='filled'
                        allowClear={false}
                        className='text-14px'
                        style={{ minWidth: 100 }}
                        options={WEEKDAY_OPTIONS}
                      />
                    </Form.Item>
                  )}

                  {/* 月日选择器 - 仅在每月时显示 */}
                  {scheduleFrequency === 'monthly' && (
                    <>
                      {/* 月份选择器（辅助选择） */}
                      <div className='mb-0'>
                        <Select
                          variant='filled'
                          allowClear={false}
                          className='text-14px'
                          style={{ minWidth: 100 }}
                          placeholder='选择月份'
                          value={selectedMonth}
                          onChange={handleMonthChange}
                          options={MONTH_OPTIONS}
                        />
                      </div>

                      <Form.Item
                        name={['execution_plan', 'schedule', 'day_of_month']}
                        required={false}
                        rules={[{ required: true, message: '请选择日期' }]}
                        className='mb-0'
                      >
                        <Select
                          variant='filled'
                          allowClear={false}
                          className='text-14px'
                          style={{ minWidth: 100 }}
                          placeholder='选择日期'
                          options={monthDayOptions}
                        />
                      </Form.Item>
                    </>
                  )}

                  {/* 日期选择器 - 仅在不重复时显示 */}
                  {scheduleFrequency === 'once' && (
                    <Form.Item
                      name={['execution_plan', 'schedule', 'execution_date']}
                      required={false}
                      rules={[{ required: true, message: '请选择执行日期' }]}
                      className='mb-0'
                      getValueFromEvent={date =>
                        date ? date.format('YYYY-MM-DD') : null
                      }
                      getValueProps={value => ({
                        value: value ? dayjs(value, 'YYYY-MM-DD') : null,
                      })}
                    >
                      <DatePicker
                        variant='filled'
                        allowClear={false}
                        className='text-14px'
                        style={{ minWidth: 120 }}
                        placeholder='选择日期'
                        disabledDate={current => {
                          // 禁用今天之前的日期
                          return current && current.isBefore(dayjs(), 'day')
                        }}
                      />
                    </Form.Item>
                  )}

                  <Form.Item
                    name={['execution_plan', 'schedule', 'time']}
                    required={false}
                    rules={[{ required: false, message: '请选择执行时间' }]}
                    className='mb-0'
                    getValueFromEvent={time =>
                      time
                        ? time.format('HH:mm')
                        : getCurrentDefaults().defaultTime
                    }
                    getValueProps={value => ({
                      value: value
                        ? dayjs(value, 'HH:mm')
                        : dayjs(getCurrentDefaults().defaultTime, 'HH:mm'),
                    })}
                  >
                    <TimePicker
                      variant='filled'
                      allowClear={false}
                      format='HH:mm'
                      className='text-14px'
                    />
                  </Form.Item>
                </div>
              </div>
            )}

            {/* Task Steps */}
            <Form.Item
              label={
                isScheduleTask && (
                  <span className='text-14px font-500 text-#17171D'>
                    任务步骤
                    <span className='text-red-500 ml-4px'>*</span>
                  </span>
                )
              }
            >
              <Form.List name='steps'>
                {fields => (
                  <div className='space-y-24px'>
                    {fields.map((field, index) => {
                      return (
                        <div key={field.key} className='relative'>
                          <div className='flex rounded-12px b-1 b-solid b-#14626999/8 p-12px hover:bg-[rgba(246,246,249)] relative group'>
                            <div className='pr-12px h-100% flex items-center justify-center text-16px font-500 c-[rgba(123,97,255,0.7)]'>
                              {index + 1}
                            </div>

                            <div className='flex-1 space-y-12px pl-12px'>
                              {/* Markdown Editor */}
                              <Form.Item
                                className='mb-22px'
                                name={[field.name, 'content']}
                                rules={[
                                  {
                                    required: true,
                                    message: '请输入任务步骤内容',
                                  },
                                ]}
                              >
                                <TaskMarkdownEditor
                                  disabled={disabled}
                                  placeholder={`请输入第${index + 1}步任务步骤`}
                                />
                              </Form.Item>

                              {/* Agent Selection */}
                              <div className='flex items-center space-x-8px text-14px text-#8D8D99'>
                                <Form.Item
                                  name={[field.name, 'agent_id']}
                                  className='mb-0'
                                  hidden={true}
                                >
                                  <Select
                                    variant='filled'
                                    popupMatchSelectWidth={false}
                                    className='text-14px text-gray-700 [&_.ant-select-selector]:rd-full [&_.ant-select-selector]:b-transparent!'
                                    options={agentOptions}
                                  />
                                </Form.Item>
                              </div>

                              {/* Hidden fields for id and title */}
                              <Form.Item name={[field.name, 'id']} hidden>
                                <Input />
                              </Form.Item>
                              <Form.Item name={[field.name, 'title']} hidden>
                                <Input />
                              </Form.Item>
                            </div>

                            {/* Delete Button */}
                            {fields.length > 1 && !disabled && (
                              <IconButton
                                icon='i-icons-delete-fill'
                                size='size-20px'
                                iconSize='size-20px'
                                className='hover:text-red-500 top-12px right-12px absolute hidden group-hover:flex'
                                onClick={() => removeStep(index)}
                              />
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </Form.List>
            </Form.Item>

            {/* Add Step Button */}
            <div className='text-center w-100%'>
              <Button
                onClick={addNewStep}
                className='text-14px w-100%'
                disabled={disabled}
              >
                + 添加子任务
              </Button>
            </div>
          </Form>
        </ScrollView>
      </div>

      {/* Footer */}
      <div
        className={classNames('shrink-0', {
          'py-12px': isScheduleTask,
          'py-24px': !isScheduleTask,
        })}
      >
        {showExtraFooter ? (
          <div className='flex justify-end items-center'>
            <div className='mr-auto'>
              <Button disabled={disabled} danger onClick={_handleDelete}>
                删除
              </Button>
            </div>
            <div className='flex space-x-12px'>
              <Button disabled={disabled} onClick={_handleClose}>
                取消
              </Button>
              <Button
                type='primary'
                disabled={disabled}
                onClick={() => {
                  handleStart()
                }}
              >
                保存
              </Button>
            </div>
          </div>
        ) : (
          <div className='flex justify-center'>
            <Button type='primary' onClick={handleStart} disabled={disabled}>
              开始运行
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default TaskPlan

interface TaskModalProps {
  onConfirm?: (data: TaskPlanType) => void
  onCancel?: () => void
  onDelete?: (groupId: string) => void
  groupId: string
}

export const TaskPlanModal = NiceModal.create(
  ({ onConfirm, onCancel, onDelete, groupId }: TaskModalProps) => {
    const { visible, hide, remove } = NiceModal.useModal()

    const [agentTeamList, setAgentTeamList] = useState<AgentInfo[]>([])
    const [teamInfo, setTeamInfo] = useState<
      Omit<TeamDetailResponse, 'sub_agents'> | undefined
    >(undefined)
    const [config, setConfig] = useState<
      PlanConfigProps['planConfig'] | undefined
    >(undefined)

    const handleClose = () => {
      onCancel?.()
      hide()
    }

    const handleStart = async (data: TaskPlanType) => {
      await onConfirm?.(data)
      hide()
    }

    const handleDelete = async () => {
      await onDelete?.(groupId)
      hide()
    }

    const { loading } = useRequest(
      async () => {
        const res = await getAutomationTaskDetail(groupId)
        const agentTeamDetail = await getAgentTeamDetail(res.execution_team_id)
        console.log(res, agentTeamList, agentTeamDetail, 're418238')

        const list = res.task_plan.steps.map(step => ({
          ...step,
          step_name: step.step_name,
          agent_id: step.agent_id,
        }))
        const config = {
          title: res.name,
          team_name: agentTeamDetail.team_name,
          team_id: agentTeamDetail.team_id,
          execution_plan: res.execution_plan,
          steps: list,
        }

        setConfig(config)
        setAgentTeamList(agentTeamDetail.sub_agents)
        setTeamInfo(omit(agentTeamDetail, ['sub_agents']))
      },
      {
        // 当组件挂载时自动执行
        refreshDeps: [groupId],
        onError: error => {
          console.error('获取任务详情失败:', error)
          message.error('获取任务详情失败')
        },
      },
    )

    return (
      <Modal
        open={visible}
        styles={{ body: { padding: 0 } }}
        width='750px'
        title='编辑定时任务'
        footer={null}
        afterClose={remove}
        destroyOnClose
        onCancel={handleClose}
      >
        <Spin spinning={loading} tip='加载中...'>
          <div className='h-80vh'>
            <TaskPlan
              agentTeamList={agentTeamList}
              teamInfo={teamInfo}
              config={config}
              taskType='schedule'
              onClose={handleClose}
              onStart={handleStart}
              onDelete={handleDelete}
              hiddenHeader={true}
              showExtraFooter={true}
              className='h-full flex flex-col bg-white'
            />
          </div>
        </Spin>
      </Modal>
    )
  },
)

export function TaskPlanPanel() {
  const { hidePanel } = useTaskPanel()
  const { sendMessage, teamAgentList, teamInfo, isShareMode } = useNextAgent()
  const latestEvent = useLatestTaskEvent()
  const eventList = useTaskEventList()
  const updateScheduleList = useTaskListStore(s => s.updateScheduleList)
  const group = useTaskListStore(s => s.group)
  const taskType = group === 'daily' ? 'task' : 'schedule'
  const isSchedule = taskType === 'schedule'

  const planConfig = useMemo(() => {
    // 从 eventList 中查找符合条件的事件
    let targetEvent = null

    // 遍历所有步骤和事件
    for (const step of eventList) {
      for (const event of step.events) {
        if (
          event.event_type === EventType.TEXT &&
          (event.content as any)?.action_type === ToolType.TEXTJSON
        ) {
          targetEvent = event
          break // 找到后退出内层循环
        }
      }
      if (targetEvent) break // 找到后退出外层循环
    }
    return targetEvent?.renderProps?.planConfig
  }, [eventList])

  const canEditTaskPlan = useMemo(() => {
    return (
      latestEvent?.event_type === EventType.TEXT &&
      (latestEvent?.content as any)?.action_type === ToolType.TEXTJSON &&
      !isShareMode
    )
  }, [latestEvent, isShareMode])

  const handleStart = useCallback(
    (values: TaskPlanType) => {
      const steps = values.steps

      const pickedSteps = steps.map((step: any) => ({
        step_detail: step.step_detail,
        step_id: step.step_id,
        step_name: step.step_name,
        step_number: step.step_number,
        agent_name: step.agent_name,
        agent_id: step.agent_id,
        content: step.content,
      }))

      // 构建任务配置对象
      const config = {
        ...values,
        // 根据任务类型决定是否添加执行计划
        execution_plan: isSchedule
          ? { schedule: values.execution_plan?.schedule }
          : undefined,
        // 设置团队ID
        team_id: teamInfo?.team_id,
        // 定时任务需要设置definition_id
        definition_id: isSchedule ? group : undefined,
        // 使用处理过的步骤数据
        steps: pickedSteps,
      }

      sendMessage({
        message_type: ToolType.Start,
        content: JSON.stringify(config),
      })

      if (taskType === 'schedule') {
        setTimeout(() => {
          updateScheduleList(true)
        }, 3000)
      }

      hidePanel()
    },
    [
      isSchedule,
      teamInfo?.team_id,
      group,
      sendMessage,
      taskType,
      updateScheduleList,
      hidePanel,
    ],
  )

  return (
    <TaskPlan
      taskType={taskType}
      agentTeamList={teamAgentList}
      teamInfo={teamInfo}
      config={planConfig}
      disabled={!canEditTaskPlan}
      onStart={handleStart}
    />
  )
}
