import type { TeamListItem } from '@apis/mindnote/team'
import { getUserTeamList } from '@apis/mindnote/team'
import { useMemoizedFn, useRequest } from 'ahooks'
import { memo, useEffect } from 'react'
import classNames from 'classnames'
import { ScrollView } from '@/components/base/scroll-view'

interface TeamSelectProps {
  value?: string
  onChange?: (value: string) => void
}

export const TeamSelect = memo((props: TeamSelectProps) => {
  const { value, onChange } = props
  const { data: teamList = [] } = useRequest(getUserTeamList)

  const handleChange = useMemoizedFn((team: TeamListItem) => {
    onChange?.(team.team_id)
    document.dispatchEvent(new CustomEvent('team-change', { detail: team }))
  })

  useEffect(() => {
    if (!value && teamList.length > 0) {
      handleChange?.(teamList[0])
    }
  }, [teamList, value, onChange])

  if (!teamList.length) return null

  return (
    <div className='mb-36px'>
      <ScrollView>
        <div className='flex justify-between pb-8px'>
          {teamList.map(each => {
            return (
              <div
                key={each.team_id}
                className={classNames(
                  'flex-none w-257px h-125px bg-cover rd-30px p-24px cursor-pointer grayscale-100',
                  {
                    'grayscale-0!': value === each.team_id,
                  },
                )}
                style={{
                  backgroundImage: `url(${each.background})`,
                  backgroundPosition: '0px -100px',
                }}
                onClick={() => handleChange(each)}
              >
                <h2>{each.team_name}</h2>
                <p>{each.team_desc}</p>
              </div>
            )
          })}
        </div>
      </ScrollView>
    </div>
  )
})
