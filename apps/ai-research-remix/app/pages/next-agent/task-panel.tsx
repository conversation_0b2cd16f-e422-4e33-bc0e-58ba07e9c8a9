import React, { useEffect } from 'react'
import { cn } from '@bty/util'
import { Tabs, useTaskPanel } from '@/store/task'
import { useTaskListStore } from '@/store/task-list'
import { GapBar } from '@/components/base/gap-bar'
import { useTeamTheme } from '@/store/team'
import { TaskPanelArtifacts } from './task-panel-artifacts'
import { TaskPanelComputer } from './task-panel-computer'
import { TaskPlanPanel } from './task-plan'
import { useNextAgent } from './provider/NextAgentProvider'

export const TaskPanel = React.memo(() => {
  const { teamId } = useNextAgent()

  const teamTheme = useTeamTheme(teamId)
  const { panelVisible, activeTab } = useTaskPanel()

  const setShowList = useTaskListStore(s => s.setShow)

  useEffect(() => {
    if (panelVisible) {
      setShowList(false)
    }
  }, [panelVisible])

  const content = !panelVisible ? null : activeTab === Tabs.Computer ? (
    <TaskPanelComputer />
  ) : activeTab === Tabs.Artifact ? (
    <TaskPanelArtifacts />
  ) : activeTab === Tabs.Plan ? (
    <TaskPlanPanel />
  ) : null

  return (
    <>
      {panelVisible && <GapBar />}
      <div
        className={cn(
          'sm:shrink-0 h-full flex flex-col transition-all duration-300 ease-in-out rounded-24px overflow-hidden',
          panelVisible ? 'w-1/2 opacity-100' : 'w-0 opacity-0',
        )}
        style={{
          background:
            activeTab === Tabs.Computer ? teamTheme.sandbox : teamTheme.chat,
        }}
      >
        {content}
      </div>
    </>
  )
})
