import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Markdown, Palette, Preview } from '@bty/components'
import {
  getArtifacts,
  getFileUrl,
  getShareArtifacts,
  getShareFileUrl,
} from '@apis/mindnote/next-agent-chat'
import type { TaskArtifact } from '@apis/mindnote/next-agent-chat.type'
import { cn } from '@bty/util'
import {
  useTaskArtifact,
  useTaskArtifactFront,
  useTaskArtifactList,
} from '@/store/task-event'
import { download } from '@/util/download'
import { Image } from '../../components/base/image'
import { useTaskListStore } from '@/store/task-list'
import { TaskLoading } from './task/task-loading'
import { TaskArtifactList } from './task/task-artifact-list'
import { supportedLangs } from './task/task-code'
import { zipHtml, openHtml, Html, copyLink } from './task/task-html'
import { useNextAgent } from './provider/NextAgentProvider'
import { Action } from './utils'
import { TaskArtifactHeader } from './task/task-artifact-header'
import { Slides } from './components/slides'

export const TaskPanelArtifacts = React.memo(() => {
  const taskId = useTaskListStore(s => s.taskId)

  const { isShareMode } = useNextAgent()

  const { add, open, close, clear } = useTaskArtifact()

  const artifacts = useTaskArtifactList()

  const frontArtifact = useTaskArtifactFront()

  const [isSliding, setIsSliding] = useState(!!frontArtifact)

  const [url, setUrl] = useState<string>()

  useEffect(() => {
    clear()
    const fetchFn = isShareMode ? getShareArtifacts : getArtifacts
    fetchFn(taskId!).then(artifacts => {
      add(...artifacts)
    })
  }, [])

  useEffect(() => {
    setUrl(undefined)
    setIsSliding(false)

    // 目前只有 handleBackToList 置空 frontArtifact
    if (!frontArtifact) {
      return
    }

    setIsSliding(true)

    const fn = isShareMode ? getShareFileUrl : getFileUrl
    fn(
      taskId!,
      frontArtifact.path,
      frontArtifact.file_type === 'pptx'
        ? { params: { 'x-oss-process': 'doc/preview,print_1,copy_1,export_1' } }
        : undefined,
    ).then(setUrl)
  }, [frontArtifact, isShareMode])

  const handleFileClick = useCallback((artifact: TaskArtifact) => {
    open(artifact)
    setIsSliding(true)
  }, [])

  const handleBackToList = useCallback(() => {
    setIsSliding(false)
    // 等待动画结束后清空选中文件
    setTimeout(close, 300)
  }, [])

  const ref = useRef<{ getContent: () => string | undefined }>(null)

  const handleAction = useCallback(
    async (artifact: TaskArtifact, action: string) => {
      switch (action) {
        case Action.CopyLink:
          copyLink(artifact.path, taskId!)
          return
        case Action.OpenLink:
          openHtml(artifact.path, taskId!)
          return
        case Action.Copy: {
          const content = ref.current?.getContent()
          if (content) {
            navigator.clipboard.writeText(content)
          }
          return
        }
        case Action.Download: {
          if (artifact.file_type === 'html') {
            zipHtml(artifact.path, taskId!)
            return
          }
          if (url) {
            download({ url, name: artifact.file_name || '未知文件' })
          }
          return
        }
        case Action.ExportPdf: {
          try {
            const response = await fetch(
              `${import.meta.env.BASE_URL}file_convert/to_pdf`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  filepath: artifact.path,
                  taskId,
                }),
              },
            )

            if (response.ok) {
              const blob = await response.blob()
              const blobUrl = URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = blobUrl
              link.download = artifact.file_name.replace('.md', '.pdf')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              URL.revokeObjectURL(blobUrl)
            }
          } catch (_) {
            // ignore
          }
          break
        }
      }
    },
    [taskId, url],
  )

  // todo: rerender
  let children

  if (!url) {
    children = (
      <div className='p-24px h-[calc(100%-44px)] overflow-y-auto'>
        <TaskLoading className='h-full flex flex-col justify-center' />
      </div>
    )
  } else {
    switch (frontArtifact?.file_type) {
      case 'md':
        children = <Markdown ref={ref} url={url!} allowXml={false} />
        break
      case 'html':
        children = (
          <Html className='h-full' taskId={taskId!} path={frontArtifact.path} />
        )
        break
      case 'csv':
        children = (
          <Preview.CSV
            overrides={{ root: 'bg-white rounded-10px' }}
            ref={ref}
            url={url!}
          />
        )
        break
      case 'pptx': {
        children = <Slides />
        break
      }
      case 'png':
        children = <Image className='object-contain rounded-10px' src={url} />
        break
      case 'pdf':
        children = <Preview.VirtualPdf url={url!} />
        break
      default:
        if (supportedLangs.includes(frontArtifact?.file_type || '')) {
          children = (
            <Palette
              ref={ref}
              url={url!}
              lang={frontArtifact!.file_type}
              remote
            />
          )
        } else {
          children = (
            <p className='text-center text-#8d8d99 mt-100px'>
              此文件类型暂不支持预览
            </p>
          )
        }
        break
    }
  }

  const containerRef = useRef<HTMLDivElement>(null)

  return (
    <div ref={containerRef} className='h-full relative overflow-hidden'>
      <TaskArtifactHeader
        artifact={frontArtifact}
        onBack={handleBackToList}
        onAction={handleAction}
      />
      <TaskArtifactList artifacts={artifacts} onClick={handleFileClick} />
      <div
        className={cn(
          'absolute inset-x-0 top-72px transition-transform duration-300 ease-in-out p-24px h-[calc(100%-72px)] overflow-y-auto bg-white rounded-t-24px',
          isSliding ? 'translate-x-0' : 'translate-x-full',
        )}
      >
        {children}
      </div>
    </div>
  )
})
