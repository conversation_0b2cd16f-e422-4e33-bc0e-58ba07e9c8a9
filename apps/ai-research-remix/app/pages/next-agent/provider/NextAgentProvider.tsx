import type { PropsWithChildren } from 'react'
import { createContext, useContext, useMemo } from 'react'
import { noop } from 'lodash-es'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import { useChatUI } from '../hooks/useChatUI'
import { useMessage } from '../hooks/useMessage'
import { MessageListStatus } from '../hooks/useMessage/useMessageActions'

type NextAgentContextProps = ReturnType<typeof useChatUI> &
  ReturnType<typeof useMessage> & {
    isShareMode: boolean
  }

const NextAgentContext = createContext<NextAgentContextProps>({
  teamId: '',
  messageList: [],
  sendMessage: noop as any,
  abortMessage: noop as any,
  taskStatus: TaskStatus.NOT_STARTED,
  scrollRef: null as any,
  messageInputActionRef: null as any,
  conversationIsReady: true,
  autoScroll: false,
  setAutoScroll: noop as any,
  wsReadyState: undefined as any,
  isShareMode: false,
  messageListStatus: MessageListStatus.IDLE,
  conversationIsPublic: false,
  setConversationPublicStatus: noop as any,
  taskTitle: '',
  replayMessage: noop as any,
  replayCountDown: null as any,
  permission: null as any,
  teamAgentList: [],
  teamInfo: undefined,
})

export function NextAgentProvider(props: PropsWithChildren<any>) {
  const chatUIProps = useChatUI()

  const messageProps = useMessage(chatUIProps.scrollRef, props.isShareMode)

  const value = useMemo(() => {
    return {
      ...chatUIProps,
      ...messageProps,
      isShareMode: props.isShareMode,
      conversationType: props.type,
      // todo: 抽出去
      colors: {
        1: {
          starter:
            'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #D6EEFF 7%, rgba(214, 238, 255, 0) 95%), #F7F7FA',
          chat: 'linear-gradient(180deg, rgba(235, 247, 255, 0.9) 7%, rgba(235, 247, 255, 0) 92%), #F7F7FA',
          sandbox:
            'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(155, 213, 255, 0.4) 0%, #9BD5FF 7%), #FFFFFF',
        },

        2: {
          starter:
            'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #FFDBED 7%, rgba(255, 219, 237, 0) 95%), #F7F7FA',
          chat: 'linear-gradient(180deg, rgba(255, 240, 247, 0.9) 7%, rgba(255, 240, 247, 0) 92%), #F7F7FA',
          sandbox:
            'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(255, 174, 215, 0.4) 0%, #FFAED7 7%), #FFFFFF',
        },

        3: {
          starter:
            'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #EDD6FF 7%, rgba(237, 214, 255, 0) 95%), #F7F7FA',
          chat: 'linear-gradient(180deg, rgba(246, 235, 255, 0.89) 7%, rgba(246, 235, 255, 0) 92%), #F7F7FA',
          sandbox:
            'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(221, 179, 255, 0.4) 0%, #DDB3FF 7%), #FFFFFF',
        },
      },
    }
  }, [chatUIProps, messageProps, props.type, props.isShareMode])

  return (
    <NextAgentContext.Provider value={value}>
      {props.children}
    </NextAgentContext.Provider>
  )
}

export function useNextAgent() {
  const context = useContext(NextAgentContext)
  if (!context) {
    throw new Error('useNextAgent must be used within a NextAgentProvider')
  }
  return context
}
