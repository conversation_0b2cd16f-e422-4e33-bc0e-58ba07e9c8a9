import {
  memo,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useMemoizedFn } from 'ahooks'
import classNames from 'classnames'
import { Button, Segmented, Tooltip, message } from 'antd'
import {
  ChatModeEnum,
  EventActionType,
  EventType,
  TaskStatus,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import { InputFile } from '@bty/chat/ui'
import type { MessageFile } from '@bty/global-types/message'
import { PlaceholderEditor } from '@bty/components/editor/placeholder-editor'
import type { IPERef } from '@bty/components/editor/placeholder-editor'
import { Icon } from '../../components/base/icon'
import { useLatestTaskEvent } from '@/store/task-event'
import { useCompositing } from '@/hooks/use-compositing'
import { BLANK_CONVERSATION_ID, useTaskListStore } from '@/store/task-list'
import { ScrollView } from '@/components/base/scroll-view'
import { useNextAgent } from './provider/NextAgentProvider'
import { BusinessFileUpload } from './components/business-file-upload'

// import { ResearchConfig } from './research-config'
import { useNextAgentEvent } from './provider/NextAgentEventProvider'
import { statusTextMap, TEMPLATE_MAP } from './const'
import type { SendMessagePayload } from './types/message'
import { TeamSelect } from './team-select'
import { MessageListStatus } from './hooks/useMessage/useMessageActions'

// import type { MCPConfigRef } from './mcp-popover'
// import { MCPConfig } from './mcp-popover'

const OPTIONS = [
  {
    label: (
      <div className='flex-center gap-6px text-14px text-#3F3F44 h-28px'>
        <Icon icon='i-icons-step-team' />
        <span>深度</span>
      </div>
    ),
    value: ChatModeEnum.TEAM,
  },
  {
    label: (
      <div className='flex-center gap-6px text-14px text-#3F3F44 h-28px'>
        <Icon icon='i-icons-step-single' />
        <span>快速</span>
      </div>
    ),
    value: ChatModeEnum.SINGLE,
  },
]

export const NextAgentMessageInput = memo<{
  selectTeamId: string
  handleTeamChange: (teamId: string) => void
}>(({ selectTeamId, handleTeamChange }) => {
  const {
    sendMessage,
    taskStatus,
    abortMessage,
    messageInputActionRef,
    wsReadyState,
    isShareMode,
    replayMessage,
    messageListStatus,
  } = useNextAgent()

  const taskId = useTaskListStore(s => s.taskId)
  const isSchedule = useTaskListStore(s => s.tab === 'schedule')
  const createTask = useTaskListStore(s => s.createTask)
  const updateTask = useTaskListStore(s => s.updateTask)
  const updateScheduleList = useTaskListStore(s => s.updateScheduleList)

  const latestEvent = useLatestTaskEvent()

  const isPlanEvent = useMemo(() => {
    return (
      latestEvent?.event_type === EventType.TEXT &&
      (latestEvent?.content as any)?.action_type === ToolType.TEXTJSON
    )
  }, [latestEvent?.event_type])

  const [compositing, handleComposition] = useCompositing()

  const { onMessageSendHook } = useNextAgentEvent()
  const [chatMode, setChatMode] = useState(ChatModeEnum.TEAM)
  const [content, setContent] = useState('')
  const [uploadedFile, setUploadedFile] = useState<MessageFile | null>(null)
  const eRef = useRef<IPERef>()

  const taskIsRunning = taskStatus === TaskStatus.IN_PROGRESS
  const taskIsPause = taskStatus === TaskStatus.PAUSE
  const taskIsCompleted = [
    TaskStatus.COMPLETED,
    TaskStatus.FAILED,
    TaskStatus.CANCELED,
  ].includes(taskStatus)

  const isAskUser = useMemo(() => {
    return taskIsCompleted
      ? false
      : latestEvent?.event_type === EventType.ASK_USER &&
          latestEvent?.content?.action_type === EventActionType.AskUser
  }, [
    latestEvent?.event_type,
    latestEvent?.content?.action_type,
    taskIsCompleted,
  ])

  const handleSubmit = useMemoizedFn((payload: SendMessagePayload) => {
    onMessageSendHook?.()
    const fileIds = uploadedFile?.upload_file_id
      ? [uploadedFile.upload_file_id]
      : []
    const submissionPayload = {
      ...payload,
      team_id: selectTeamId,
      upload_file_ids: fileIds.length > 0 ? fileIds : undefined,
    }

    if (taskId && taskId === BLANK_CONVERSATION_ID) {
      // appendEvent({
      createTask(selectTeamId).then(
        async ({
          conversation_id: returnConversationId,
          team_id: returnTeamId,
        }) => {
          if (returnConversationId) {
            handleTeamChange(returnTeamId)
            // await mcpConfigRef.current?.triggerMcp(returnConversationId)
            setTimeout(() => {
              sendMessage(
                {
                  ...submissionPayload,
                  team_id: returnTeamId,
                },
                returnConversationId,
              )
              setTimeout(() => {
                if (isSchedule) {
                  updateScheduleList(true)
                }
              }, 5000)
            }, 50)
          }
        },
      )
    } else {
      sendMessage(submissionPayload)
    }
  })

  const handleFileChange = useMemoizedFn((file: MessageFile) => {
    setUploadedFile(file)
  })

  useImperativeHandle(messageInputActionRef, () => ({
    setContent: (text: string) => {
      setContent(text)
      if (eRef.current) {
        eRef.current?.setTemplate?.(text)
      }
    },
  }))

  useEffect(() => {
    // 任何 conversationId（taskId）变化都清空内容和文件
    setUploadedFile(null) // 清空文件
  }, [taskId])

  const handleChangeEditor = useMemoizedFn(() => {
    const temp = TEMPLATE_MAP[isSchedule ? 'default' : ''] ?? ''
    setTimeout(() => {
      eRef.current?.setTemplate?.(temp)
    }, 50)
  })

  useEffect(() => {
    if (!taskId || taskId === BLANK_CONVERSATION_ID) {
      handleChangeEditor()
    }
  }, [isSchedule])

  useEffect(() => {
    document.addEventListener('create-task', handleChangeEditor)
    return () => {
      document.removeEventListener('create-task', handleChangeEditor)
    }
  }, [])

  useEffect(() => {
    if (
      latestEvent?.event_type === EventType.ASK_USER &&
      latestEvent?.content?.action_type === EventActionType.AskUser &&
      eRef.current
    ) {
      eRef.current.focus()
    }
  }, [latestEvent?.event_type, latestEvent?.content?.action_type, eRef.current])

  const tooltipPopContainerRef = useRef<HTMLDivElement | null>(null)

  const showStatusText =
    [TaskStatus.IN_PROGRESS].includes(taskStatus) || isShareMode

  const isEmptyTask = useMemo(() => {
    return !taskId || taskId === BLANK_CONVERSATION_ID
  }, [taskId])

  const isScheduleTask = useMemo(() => {
    return taskId && taskId !== BLANK_CONVERSATION_ID && isSchedule
  }, [taskId, isSchedule])

  const isMessageListInactive =
    messageListStatus === MessageListStatus.NOT_FOUND ||
    messageListStatus === MessageListStatus.IDLE ||
    messageListStatus === MessageListStatus.LOADING ||
    messageListStatus === MessageListStatus.ERROR

  if (isShareMode && isMessageListInactive) {
    return null
  }

  return (
    <>
      {isEmptyTask && (
        <TeamSelect value={selectTeamId} onChange={handleTeamChange} />
      )}
      <div
        className='group relative p-[2px] box-border bg-white rounded-[12px] items-center border-[#E1E1E5] border border-solid border-opacity-60 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] box-border'
        onClick={() => {
          if (eRef.current) {
            eRef.current.focus()
          }
        }}
      >
        <div className='flex items-center'>
          {isAskUser ? (
            <div className='w-100% border-1px px-12px h-66px border-solid border-[rgba(225,225,229,0.6)] rounded-[12px_12px_0_0] absolute left-0 top-[-45px]  z-0 text-14px c-font items-start'>
              <div className='mt-12px flex items-center justify-between'>
                <div className='flex items-center'>
                  <Icon
                    icon='i-icons-next-agent-task-running'
                    className='!size-18px mr-8px'
                  />
                  <p>正在等待你的回复...</p>
                </div>
                <div
                  className='w-24px h-24px rounded-full flex-center bg-gradient-to-br from-[#36CDFF] via-[#684AFF] to-[#963AFF] cursor-pointer hover:op-80'
                  onClick={e => {
                    e.stopPropagation()
                    if (taskIsPause) {
                      abortMessage()
                      if (taskId) {
                        updateTask(taskId, {
                          task_state: TaskStatus.CANCELED,
                        })
                      }
                    }
                  }}
                >
                  <Icon icon='i-icons-pause-white' className='!size-8px' />
                </div>
              </div>
            </div>
          ) : (
            <div className='absolute invisible group-focus-within:visible right-0 top-[-28px] z-[10] pointer-events-none p-4px c-#8D8D99/80 text-12px flex items-center rd-4px bg-#fff/90 backdrop-blur-10px'>
              <Icon icon='i-icons-key-enter' className='!size-12px mr-4px' />
              发送，
              <Icon icon='i-icons-key-shift' className='!size-12px' />
              <Icon
                icon='i-icons-key-enter'
                className='!size-12px ml-2px mr-4px'
              />
              换行 ，
              <Icon icon='i-icons-key-cmd' className='!size-9px mr-2px' />K
            </div>
          )}
        </div>

        <div className='hidden absolute inset-0 pointer-events-none rounded-[12px] border-0 bg-[conic-gradient(from_var(--angle),#36CDFF,#684AFF,#963AFF,#36CDFF)] group-focus-within:block animate-[rotate_16s_linear_infinite]'></div>
        <div className='chat-input-wrapper flex items-center relative z-[2] min-h-[50px] w-full justify-between bg-white rounded-[10px] box-border p-[9px]'>
          {showStatusText ||
          isPlanEvent ||
          (isScheduleTask && taskIsCompleted) ? (
            <div className='text-14px/14px c-font flex items-center'>
              <Icon
                icon='i-icons-next-agent-task-running'
                className='!size-18px mr-8px'
              />
              <span>{statusTextMap[taskStatus]}</span>
            </div>
          ) : (
            <div className='w-100%'>
              {uploadedFile && (
                <div className='mb-10px inline-block'>
                  <InputFile
                    key={uploadedFile.upload_file_id}
                    file={{
                      name: uploadedFile.name,
                      type: uploadedFile.type,
                      url: uploadedFile.url,
                    }}
                    className='rd-8px'
                    onFileClick={() => {
                      console.log('uploadedFile', uploadedFile)
                      if (uploadedFile.url) {
                        window.open(uploadedFile.url, '_blank')
                      }
                    }}
                    onDelete={() => {
                      setUploadedFile(null)
                    }}
                  />
                </div>
              )}
              <div
                className={classNames('relative text-[16px]/[24px]', {
                  'min-h-100px': isEmptyTask,
                })}
                onCompositionStart={handleComposition}
                onCompositionEnd={handleComposition}
                onKeyDown={e => {
                  if (compositing.current) return
                  if (e.key === 'Enter') {
                    if (e.shiftKey) {
                      return // 允许换行
                    }
                    e.preventDefault() // 阻止默认的换行行为
                    if (wsReadyState !== 1) {
                      message.error('连接已断开，请刷新页面重试')
                      return
                    }
                    if (content.trim() && !taskIsRunning) {
                      handleSubmit({
                        content: content.trim(),
                        chat_mode:
                          isEmptyTask && !isSchedule ? chatMode : undefined,
                        team_id: selectTeamId,
                      })
                    }
                  }
                }}
              >
                <ScrollView scrollClassName='max-h-192px'>
                  <PlaceholderEditor eRef={eRef} onChange={setContent} />
                  {(!content || content === '\n') && (
                    <div className='absolute top-0 left-0 z-[-1]'>
                      <span className='leading-24px event-none c-#8D8D99/60'>
                        问我任何问题...
                      </span>
                    </div>
                  )}
                </ScrollView>
              </div>

              <div
                className='flex mt-12px'
                onMouseDown={e => {
                  e.stopPropagation()
                  e.preventDefault()
                }}
              >
                {isEmptyTask && !isSchedule && (
                  <Segmented
                    size='small'
                    className='h-32px rd-8px [&_.ant-segmented-group]:gap-2px'
                    style={
                      { '--ant-border-radius-xs': '6px' } as React.CSSProperties
                    }
                    options={OPTIONS}
                    value={chatMode}
                    onChange={setChatMode}
                  />
                )}

                <BusinessFileUpload
                  className='ml-auto mr-10px w-32px h-32px flex-center rd-8px hover:bg-[#EEEEF6]/70 cursor-pointer'
                  onFileChange={handleFileChange}
                />
              </div>
              {/* <MCPConfig ref={mcpConfigRef} /> */}
            </div>
          )}

          <div
            className={classNames('ml-auto mt-auto', {
              'mb-auto': showStatusText,
            })}
          >
            {!isShareMode && (
              <Tooltip
                title={taskIsRunning && '停止生成'}
                getPopupContainer={() =>
                  tooltipPopContainerRef.current || document.body
                }
                destroyTooltipOnHide
              >
                <div
                  ref={tooltipPopContainerRef}
                  className={classNames(
                    'rounded-full flex-center bg-gradient-to-br from-[#36CDFF] via-[#684AFF] to-[#963AFF] cursor-pointer hover:op-80',
                    {
                      '!opacity-50 cursor-not-allowed':
                        !content.trim() && !taskIsRunning,
                      'w-24px h-24px': taskIsRunning,
                      'w-32px h-32px': !taskIsRunning,
                    },
                  )}
                  onClick={e => {
                    e.stopPropagation()
                    if (taskIsRunning) {
                      abortMessage()
                    } else {
                      if (!content.trim()) return
                      handleSubmit({
                        content: content.trim(),
                        chat_mode:
                          isEmptyTask && !isSchedule ? chatMode : undefined,
                        team_id: selectTeamId,
                      })
                      eRef.current?.setTemplate?.('')
                    }
                  }}
                >
                  {taskIsRunning ? (
                    <Icon icon='i-icons-pause-white' className='!size-8px' />
                  ) : (
                    <Icon icon='i-icons-send-white' className='!size-12px' />
                  )}
                </div>
              </Tooltip>
            )}
            {isShareMode && (
              <div className='flex items-center gap-12px'>
                <Button
                  type='primary'
                  className='text-14px/14px  cursor-pointer h-32px'
                  onClick={() => {
                    replayMessage(false, true)
                  }}
                >
                  直接展示
                </Button>
                <Button
                  className='text-14px/14px  cursor-pointer h-32px'
                  onClick={() => {
                    replayMessage(true, true, true)
                  }}
                >
                  重放
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      <style>{`
        @property --angle {
          syntax: '<angle>';
          inherits: false;
          initial-value: 0deg;
        }

        @keyframes rotate {
          to {
            --angle: 360deg;
          }
        }
      `}</style>
    </>
  )
})
