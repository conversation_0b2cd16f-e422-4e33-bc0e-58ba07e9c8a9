import { useMemo, useState } from 'react'
import { Button } from '@bty/components'
import type { TaskPlan } from '@apis/mindnote/next-agent-chat.type'
import { ToolType } from '@apis/mindnote/next-agent-chat.type'
import classNames from 'classnames'
import { useLatestTaskEvent, type MessageRenderProps } from '@/store/task-event'
import { Icon } from '@/components/base/icon'
import { useNextAgent } from '../../provider/NextAgentProvider'
import { useTaskPanel } from '@/store/task'
import { useTaskListStore } from '@/store/task-list'

export interface PlanConfigProps {
  planConfig: MessageRenderProps['planConfig']
  base: MessageRenderProps['base']
}

function PlanConfig({ planConfig, base }: PlanConfigProps) {
  const { sendMessage, teamId, isShareMode } = useNextAgent()
  const latestEvent = useLatestTaskEvent()
  const updateScheduleList = useTaskListStore(s => s.updateScheduleList)

  const { hidePanel, panelVisible, showPlan } = useTaskPanel()

  const [isClicked, setIsClicked] = useState(false)

  const isLatest = latestEvent?.event_id === base?.event_id

  const group = useTaskListStore(s => s.group)
  const taskType = group === 'daily' ? 'task' : 'schedule'

  // 解析任务计划数据
  const planData = useMemo<TaskPlan>(() => {
    try {
      const data =
        typeof planConfig === 'string' ? JSON.parse(planConfig) : planConfig

      if (Array.isArray(data)) {
        return {
          title: '任务执行计划',
          team_id: teamId,
          steps: data || [],
        }
      } else {
        return {
          title: '任务执行计划',
          team_id: teamId,
          ...data,
          steps: data?.steps || [],
        }
      }
    } catch (error) {
      console.error('解析任务计划数据失败:', error)
      return {
        title: '任务执行计划',
        team_id: teamId,
        steps: [],
      }
    }
  }, [planConfig])

  const handleModifyPlan = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation()
    showPlan()
  }

  const handleStartExecution = (event: React.MouseEvent<HTMLButtonElement>) => {
    setIsClicked(true)
    event.stopPropagation()
    hidePanel()
    sendMessage({
      message_type: ToolType.Start,
      content:
        taskType === 'schedule'
          ? JSON.stringify({
              ...planConfig,
              definition_id: group,
            })
          : '',
    })
    if (taskType === 'schedule') {
      setTimeout(() => {
        updateScheduleList(true)
      }, 3000)
    }
  }

  return (
    <div className='w-full mx-auto'>
      <p className='text-14px text-[#17171d] mb-12px'>
        已根据相关经验生成任务方案，请确认
      </p>
      <div
        className='w-full rounded-8px p-12px mb-12px cursor-pointer'
        style={{
          background:
            'radial-gradient(29% 33% at 13% 0%, rgba(186, 173, 255, 0.28) 0%, rgba(123, 97, 255, 0) 100%), #F8F7FF',
        }}
        onClick={handleModifyPlan}
      >
        {/* 顶部提示文本 */}
        <div className='mb-16px'>
          {/* 任务方案标签 */}
          <div className='inline-flex items-center bg-#fff text-primary rounded-full font-medium gap-8px px-12px py-4px text-14px'>
            <Icon icon='i-icons-next-agent-plan_analyze' size='size-14px' />
            任务方案
          </div>
          <div className='mt-8px items-center  font-medium gap-8px pl-0px text-16px/20px'>
            {planConfig?.name && <div>{planConfig?.name}</div>}
          </div>
          {planConfig?.execution_plan?.text && (
            <div className='mt-7px items-center font-medium gap-8px pl-0px text-14px c-#8D8D99'>
              执行时间: {planConfig?.execution_plan?.text}
            </div>
          )}
        </div>

        {/* 任务步骤列表 */}
        <div className='relative'>
          {planData.steps.map((step, index) => {
            return (
              <div
                key={step.step_id}
                className='relative flex items-center last:pb-0 pb-8px'
              >
                {/* 虚线连接 */}
                {index < planData.steps.length - 1 && (
                  <div className='absolute border-l-1px border-dashed border-[rgba(197,189,249)] z-0 left-6.5px top-5px w-2px h-full' />
                )}

                {/* 紫色圆圈编号 */}
                <div className='relative flex items-center justify-center bg-[rgba(197,189,249)] rounded-full w-14px h-14px mr-16px'>
                  <span className='text-white text-12px'>{index + 1}</span>
                </div>

                {/* 任务内容 */}
                <div className='flex-1 flex items-center justify-start'>
                  <span className='text-[#17171d] text-14px'>
                    {step.step_name}
                  </span>
                </div>
              </div>
            )
          })}
        </div>

        {/* 操作按钮 */}
        <div className='flex justify-end mt-12px'>
          <Button
            className={classNames(
              'font-medium text-gray-700 bg-white border border-gray-300 rounded-8px transition-colors text-12px h-32px! px-10px!',
              {
                'cursor-not-allowed': !isLatest,
                'hover:bg-white': isLatest,
              },
            )}
            disabled={!isLatest || panelVisible || isClicked || isShareMode}
          >
            修改方案
          </Button>
          <Button
            type='primary'
            disabled={!isLatest || panelVisible || isClicked || isShareMode}
            onClick={handleStartExecution}
            className={classNames(
              'ml-12px font-medium text-white bg-purple-600 rounded-8px transition-colors text-12px h-32px! px-10px!',
              {
                'cursor-not-allowed': !isLatest,
                'hover:bg-purple-600': isLatest,
              },
            )}
          >
            开始运行
          </Button>
        </div>
      </div>
    </div>
  )
}

export default PlanConfig
