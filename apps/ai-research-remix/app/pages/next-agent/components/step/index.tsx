import type { FC } from 'react'
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react'
import classNames from 'classnames'
import { useInterval, usePrevious, useUpdateEffect } from 'ahooks'
import {
  TaskStatus,
  EventType,
  EventActionType,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type { AssigneeInfo, Event } from '@apis/mindnote/next-agent-chat.type'
import MessageItem from '../message-item'
import type { MessageItemProps } from '../message-item'
import { Icon } from '../../../../components/base/icon'
import { useTaskTiming } from '@/store/task-event'
import { formatDuration } from '../../utils'
import './sticky-header.scss'
import { RunningIndicator } from '../running-indicator'

/**
 * 步骤类型枚举 - 定义步骤的不同展示方式
 */
export enum StepType {
  /** 标准步骤 - 带有标题、折叠功能和连接线 */
  STANDARD = 'standard',
  /** 用户输入步骤 - 简单步骤，没有标题和折叠功能 */
  USER_INPUT = 'user_input',
}

/**
 * 步骤ID前缀常量
 */
export const USER_INPUT_STEP_ID_PREFIX = `${StepType.USER_INPUT}_`

/**
 * 扩展的事件类型
 */
type ExtendedEvent = Event & { renderProps: MessageItemProps }

/**
 * 事件组件属性
 */
interface EventProps {
  event: ExtendedEvent
  index: number
  supportMarkdown?: boolean
}

/**
 * 步骤组件属性
 */
export interface StepProps {
  /** 步骤标题 */
  title: string
  /** step步骤状态 */
  stepState: TaskStatus
  /** task任务状态 */
  taskState: TaskStatus
  /** 步骤包含的事件列表 */
  events: ExtendedEvent[]
  /** 是否默认折叠 */
  isCollapsed?: boolean
  /** 步骤类型 */
  type?: StepType
  /** 最新事件 */
  latestEvent?: Event
  /** 步骤开始时间 */
  stepStartTime?: string
  /** 步骤结束时间 */
  stepEndTime?: string
  /** 点击事件回调（暂未使用，保留以备后续扩展） */
  onClick?: (
    type: string,
    eventId: string,
    params?: Record<string, any>,
  ) => void
  assigneeInfo?: AssigneeInfo
  /** 是否是最后一个步骤 */
  isLastStep?: boolean
  /** 是否是分享模式 */
  isShareMode?: boolean
}

/**
 * 状态图标配置映射
 */
const STATUS_ICON_CONFIG: Partial<
  Record<TaskStatus, { icon: string; size: string } | { isSpinner: true }>
> = {
  [TaskStatus.NOT_STARTED]: {
    icon: 'i-icons-schedule-running',
    size: 'size-12px',
  },
  [TaskStatus.COMPLETED]: { icon: 'i-icons-run-success', size: 'size-12px' },
  [TaskStatus.CANCELED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.FAILED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.IN_PROGRESS]: { isSpinner: true },
  [TaskStatus.PAUSE]: { isSpinner: true },
} as const

/**
 * CSS 样式常量
 */
const STYLES = {
  // 进度点样式 - 独立的占位符，表示下一个输出正在处理中
  progressDot: 'relative size-12px z-2 flex flex-col items-center',
  // 状态图标容器样式 - 改为顶部对齐，确保在多行标题时正确对齐，添加过度动画
  statusIconContainer:
    'flex items-start justify-center rounded-full mr-3px z-1 relative w-20px h-20px pt-5px transition-all duration-300 ease-in-out',
  // 加载动画样式
  spinner:
    'relative size-12px z-2 bg-#fff rd-12px border-2 border-solid border-#625DFF border-t-transparent animate-spin',
  // 步骤标题容器样式 - 移除内部padding，添加过度动画
  titleContainer:
    'flex items-start cursor-pointer relative z-1 transition-all duration-300 ease-in-out',
  // 事件列表容器样式
  eventsContainer: 'ml-24px mr-24px',
  // 事件列表样式
  eventsList: 'flex flex-col gap-2px',
  // 统一的过度动画配置
  transition: 'transition-all duration-300 ease-in-out',
} as const

/**
 * 根据事件类型渲染对应组件
 */
const EventComponent: FC<EventProps> = ({
  event,
  index,
  supportMarkdown = false,
}) => {
  // 防御性编程：确保 renderProps 存在
  if (!event?.renderProps) {
    console.warn(`Event at index ${index} missing renderProps:`, event)
    return null
  }

  return (
    <MessageItem supportMarkdown={supportMarkdown} {...event.renderProps} />
  )
}
/**
 * 进度点组件 - 独立的占位符，表示正在处理下一个输出
 */
const ProgressDot: FC = () => (
  <div className={STYLES.progressDot}>
    <div className='animate-breath absolute inset-0 rd-[12px] bg-[#625DFF]/50' />
    <div className='mt-[3px] size-[6px] rd-[12px] bg-[#625DFF]' />
  </div>
)

/**
 * 状态图标组件 - 展示步骤当前状态
 */
const StatusIcon: FC<{
  state: TaskStatus
  latestEvent?: Event
  hasChildren?: boolean
  collapsed?: boolean
}> = ({ state, latestEvent, hasChildren, collapsed }) => {
  const config = STATUS_ICON_CONFIG[state]

  // 如果没有配置，返回 null
  if (!config) return null

  // 判断是否应该显示进度点
  const isAskUser =
    latestEvent?.event_type === EventType.ASK_USER ||
    latestEvent?.content?.action_type === EventActionType.AskUser

  // TODO: action_type后续需要改造
  const isMcp = (latestEvent?.content as any).action_type === ToolType.MCP

  const showConnectorAndDot = hasChildren && !collapsed

  // 进度点显示逻辑
  const shouldShowProgressDot =
    ((!isAskUser && state === TaskStatus.IN_PROGRESS) ||
      (isMcp && state === TaskStatus.PAUSE)) &&
    showConnectorAndDot

  // 如果需要显示进度点，直接返回进度点
  if (shouldShowProgressDot) {
    return <ProgressDot />
  }

  // 渲染加载动画
  if ('isSpinner' in config && config.isSpinner) {
    return <ProgressDot />
  }

  // 渲染普通图标 - 添加过度动画
  if ('icon' in config) {
    return (
      <div className={STYLES.transition}>
        <Icon icon={config.icon} size={config.size} />
      </div>
    )
  }

  return null
}

// const Avatar: FC<{
//   showName?: boolean
//   assigneeInfo: AssigneeInfo
// }> = ({ showName = true, assigneeInfo }) => {
//   return (
//     <div className='flex'>
//       <div
//         className={classNames(
//           'w-40px h-40px  rounded-50% flex items-center justify-center z-2',
//         )}
//       >
//         {assigneeInfo?.icon ? (
//           <Image
//             className='w-100% h-100% object-contain rounded-50%'
//             src={assigneeInfo?.icon}
//             alt={assigneeInfo?.agent_name || ''}
//           />
//         ) : (
//           <div className='w-100% h-100% bg-#625DFF/10 rounded-50% flex items-center justify-center'>
//             未知
//           </div>
//         )}
//       </div>
//       {showName && (
//         <div className='box-border pl-12px flex-1 flex overflow-hidden'>
//           <p className='text-16px/24px font-500 c-font'>
//             {assigneeInfo?.agent_name}
//           </p>
//           <p className='ml-4px text-12px/24px c-#8D8D99'>
//             {assigneeInfo?.agent_expert_name}
//           </p>
//         </div>
//       )}
//     </div>
//   )
// }

/**
 * 步骤标题组件
 */
const StepTitle: FC<{
  title: string
  state: TaskStatus
  hasChildren: boolean
  collapsed: boolean
  onToggle: () => void
  stepStartTime?: string
  stepEndTime?: string
  isTaskSummary?: boolean
  latestEvent?: Event
  isShareMode?: boolean
}> = ({
  title,
  state,
  hasChildren,
  collapsed,
  onToggle,
  stepStartTime,
  stepEndTime,
  isTaskSummary,
  latestEvent,
  isShareMode = false,
}) => {
  const [currentTime, setCurrentTime] = useState(Date.now())

  // 如果步骤正在进行中且没有结束时间，则每秒更新当前时间
  const isRunning =
    !stepEndTime &&
    stepStartTime &&
    (state === TaskStatus.IN_PROGRESS || state === TaskStatus.PAUSE)

  useInterval(
    () => {
      setCurrentTime(Date.now())
    },
    isRunning ? 1000 : undefined,
  )

  const duration = useMemo(() => {
    const endTime =
      stepEndTime ||
      (isRunning ? new Date(currentTime).toISOString() : undefined)
    const prefix = isTaskSummary ? '任务耗时 ' : ''
    return formatDuration(stepStartTime, endTime, prefix)
  }, [stepStartTime, stepEndTime, currentTime, isTaskSummary, isRunning])

  const stepNoStart = state === TaskStatus.NOT_STARTED

  return (
    <div
      style={{
        opacity: stepNoStart ? 0.6 : 1,
      }}
      className={`relative z-1 ${STYLES.transition}`}
    >
      <div className={STYLES.titleContainer} onClick={onToggle}>
        {stepNoStart && <RunningIndicator />}
        <div className={STYLES.statusIconContainer}>
          <StatusIcon
            state={state}
            hasChildren={hasChildren}
            collapsed={collapsed}
            latestEvent={latestEvent}
          />
        </div>
        <div
          className={`text-[16px] font-medium c-font leading-[20px] ${STYLES.transition}`}
        >
          {title}
          {duration && state !== TaskStatus.NOT_STARTED && !isShareMode && (
            <div
              className={`inline-flex text-12px/16px c-#8D8D99 mx-6px self-center ${STYLES.transition}`}
            >
              ({duration})
            </div>
          )}
        </div>

        {hasChildren && (
          <div className={`text-#666 pt-2px ${STYLES.transition}`}>
            <Icon
              icon='i-icons-arrow'
              size='size-16px'
              className={classNames(`c-#8D8D99 ${STYLES.transition}`, {
                'rotate-180': collapsed,
              })}
            />
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * 事件列表组件
 */
const EventsList: FC<{ events: ExtendedEvent[] }> = ({ events }) => {
  const renderEvents = useCallback(() => {
    return events.map((event, index) => (
      <EventComponent
        key={event.event_id || `event-${index}`}
        event={event}
        index={index}
      />
    ))
  }, [events])

  return <div className={STYLES.eventsList}>{renderEvents()}</div>
}

/**
 * 用户输入步骤组件 - 简化版本，只显示事件列表
 */
const UserInputStep: FC<{
  events: ExtendedEvent[]
}> = ({ events }) => (
  <div className='relative flex items-center flex-row-reverse mt-24px'>
    <div
      className={classNames(
        STYLES.eventsContainer,
        'transition-all duration-300 ease-in-out z-1111 mr-0px!',
      )}
    >
      <EventsList events={events} />
    </div>
  </div>
)

/**
 * 步骤总结组件 - 显示步骤完成时的总结信息
 */
const StepSummary: FC<{ summaryEvents: ExtendedEvent[] }> = ({
  summaryEvents,
}) => {
  if (summaryEvents.length === 0) return null

  return (
    <div className=''>
      <div className='flex flex-col gap-[4px]'>
        {summaryEvents.map((event, index) => (
          <div key={event.event_id || `summary-${index}`}>
            <EventComponent supportMarkdown event={event} index={index} />
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * 标准步骤组件 - 完整版本，包含标题、折叠功能等
 */
const StandardStep: FC<{
  title: string
  stepState: TaskStatus
  taskState: TaskStatus
  events: ExtendedEvent[]
  isCollapsed: boolean
  latestEvent?: Event
  stepStartTime?: string
  stepEndTime?: string
  assigneeInfo?: AssigneeInfo
  isLastStep?: boolean
  isShareMode?: boolean
}> = ({
  title,
  stepState,
  taskState,
  events,
  isCollapsed,
  latestEvent,
  stepStartTime,
  stepEndTime,
  isLastStep = false,
  isShareMode,
}) => {
  // 用户控制的折叠状态
  const [userCollapsed, setUserCollapsed] = useState(isCollapsed)
  const stepElementRef = useRef<HTMLDivElement>(null)
  const [_isIntersecting, setIsIntersecting] = useState(true)
  const prevStepState = usePrevious(stepState)

  // 使用 IntersectionObserver 监听元素是否在视口中 暂时hack处理样式问题
  useEffect(() => {
    const element = stepElementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          // 当元素进入或离开视口时，更新状态
          setIsIntersecting(entry.isIntersecting)
        })
      },
      {
        // 可以根据需要调整阈值
        threshold: 0, // 当元素刚进入或完全离开视口时触发
        rootMargin: '-110px 0px 0px 0px', // 可以调整边距
      },
    )

    observer.observe(element)

    // 清理 observer
    return () => {
      observer.disconnect()
    }
  }, [])

  const taskIsComplete = useMemo(() => {
    return (
      taskState === TaskStatus.COMPLETED ||
      taskState === TaskStatus.CANCELED ||
      taskState === TaskStatus.FAILED
    )
  }, [taskState])

  const stepIsComplete = useMemo(() => {
    return (
      stepState === TaskStatus.COMPLETED ||
      stepState === TaskStatus.CANCELED ||
      stepState === TaskStatus.FAILED
    )
  }, [stepState])

  // 计算最终的折叠状态：未开始的步骤强制折叠，否则使用用户控制的状态
  const shouldCollapse = useMemo(() => {
    if (stepState === TaskStatus.NOT_STARTED) {
      return true
    }
    return userCollapsed
  }, [userCollapsed, stepState])

  useUpdateEffect(() => {
    // 如果当前step的上一个状态不是完成，这次的是完成，且不是最后一个步骤，则收起
    if (
      prevStepState !== TaskStatus.COMPLETED &&
      stepState === TaskStatus.COMPLETED &&
      !isCollapsed &&
      !isLastStep
    ) {
      // 添加短暂延迟，让用户看到完成状态
      setTimeout(() => {
        setUserCollapsed(true)
      }, 800) // 延迟800ms后开始收起动画
    }
  }, [stepState, isCollapsed, isLastStep])

  // 切换折叠状态
  const handleToggle = useCallback(() => {
    setUserCollapsed(prev => !prev)
  }, [])

  const { regularEvents, summaryEvents } = useMemo(() => {
    // 分离普通事件和总结事件
    const regularEvents: typeof events = []
    const summaryEvents: typeof events = []

    events.forEach(event => {
      if (
        event.event_type === EventType.STEP_SUMMARY ||
        event.event_type === EventType.SUMMARY
      ) {
        summaryEvents.push(event)
      } else {
        regularEvents.push(event)
      }
    })

    return { regularEvents, summaryEvents }
  }, [events])

  // 判断是否有子事件
  const hasChildren = useMemo(
    () =>
      (regularEvents.length > 0 || summaryEvents.length > 0) &&
      stepState !== TaskStatus.NOT_STARTED,
    [regularEvents.length, summaryEvents.length, stepState],
  )

  // 计算实际显示的步骤状态（与 StepTitle 保持一致）
  const displayStepState = useMemo(() => {
    if (stepIsComplete) {
      return stepState
    } else {
      return taskIsComplete ? taskState : stepState
    }
  }, [stepIsComplete, taskIsComplete, taskState, stepState])

  const { taskStartTime, taskEndTime } = useTaskTiming()

  const isTaskSummary =
    stepState === TaskStatus.COMPLETED &&
    taskIsComplete &&
    !!summaryEvents?.length &&
    summaryEvents?.some(event => event.event_type === EventType.SUMMARY)

  const startTime = useMemo(() => {
    return taskIsComplete ? taskStartTime : stepStartTime
  }, [isTaskSummary, taskStartTime, stepStartTime])

  const endTime = useMemo(() => {
    return isTaskSummary ? taskEndTime : stepEndTime
  }, [isTaskSummary, taskEndTime, stepEndTime])

  return (
    <div ref={stepElementRef} className='relative mt-[60px]'>
      <div
        className={`flex flex-col rounded-[20px]! box-border ${STYLES.transition}`}
      >
        {/* 整体吸顶容器 - 包含头像和标题 */}
        <div
          className={classNames(
            'sticky-header-enhanced relative',
            STYLES.transition,
          )}
        >
          {/* 头像区域 */}
          {/* <div
            className={`box-border border-0 p-[12px] rounded-t-[12px] pt-[24px] absolute top-[-60px] left-[0px] right-[0px] ${STYLES.transition}`}
          >
            <Avatar assigneeInfo={assigneeInfo!} />
          </div> */}
          {/* 标题区域 */}
          <div
            className={classNames(
              `relative flex flex-col rounded-20px z-1 ${STYLES.transition}`,
              { 'inline-flex': shouldCollapse },
            )}
          >
            <div
              className={classNames(
                'bg-#fff border b-solid b-[rgba(225,225,229,0.8)] relative ',
                STYLES.transition,
                shouldCollapse || !_isIntersecting
                  ? 'px-12px py-9px border-b rounded-20px w-max max-w-fit'
                  : 'p-12px border-b-0 rounded-t-20px',
              )}
            >
              <StepTitle
                title={title}
                state={displayStepState}
                hasChildren={hasChildren}
                collapsed={shouldCollapse}
                onToggle={handleToggle}
                stepStartTime={startTime}
                stepEndTime={endTime}
                isTaskSummary={isTaskSummary}
                latestEvent={latestEvent}
                isShareMode={isShareMode}
              />
            </div>
          </div>
        </div>

        {/* 步骤内容部分 - 正常滚动 */}
        <div
          className={classNames(`relative flex flex-col ${STYLES.transition}`, {
            'opacity-0 transform scale-y-95 origin-top': shouldCollapse,
            'opacity-100 transform scale-y-100': !shouldCollapse,
          })}
        >
          <div
            className={classNames(
              `b-t-0 b-1 b-solid b-[rgba(225,225,229,0.8)] pt-12px relative bg-#fff px-12px pb-24px mb-0px rounded-b-20px ${STYLES.transition}`,
              {
                'b-0! overflow-hidden max-h-0 !py-0': shouldCollapse,
                'max-h-[12000px] w-100%': !shouldCollapse,
              },
            )}
          >
            {/* 事件列表 - 始终渲染，通过样式控制显示/隐藏 */}
            {hasChildren && (
              <div
                className={classNames(
                  STYLES.eventsContainer,
                  STYLES.transition,
                )}
              >
                <EventsList events={regularEvents} />
                {/* 步骤总结 - 完全独立于步骤主体，显示在虚线外面 */}
                {stepState === TaskStatus.COMPLETED &&
                  summaryEvents.length > 0 && (
                    <div className={STYLES.transition}>
                      <StepSummary summaryEvents={summaryEvents} />
                    </div>
                  )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * 步骤组件 - 展示任务的执行步骤
 *
 * 支持两种类型：
 * - STANDARD: 标准步骤，带有标题、折叠功能和连接线
 * - USER_INPUT: 用户输入步骤，简单展示事件列表
 */
export const Step: FC<StepProps> = ({
  title,
  stepState,
  taskState,
  events = [], // 提供默认值
  isCollapsed = false,
  type = StepType.STANDARD,
  latestEvent,
  stepStartTime,
  stepEndTime,
  assigneeInfo,
  isLastStep = false,
  isShareMode = false,
}) => {
  // 防御性编程：确保 events 是数组
  const safeEvents = Array.isArray(events) ? events : []

  // 根据步骤类型渲染不同的组件
  if (type === StepType.USER_INPUT) {
    return <UserInputStep events={safeEvents} />
  }

  return (
    <StandardStep
      title={title}
      stepState={stepState}
      taskState={taskState}
      events={safeEvents}
      isCollapsed={isCollapsed}
      latestEvent={latestEvent}
      stepStartTime={stepStartTime}
      stepEndTime={stepEndTime}
      assigneeInfo={assigneeInfo}
      isLastStep={isLastStep}
      isShareMode={isShareMode}
    />
  )
}

export default React.memo(Step)
