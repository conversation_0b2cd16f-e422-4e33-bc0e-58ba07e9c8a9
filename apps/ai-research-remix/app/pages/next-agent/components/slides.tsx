import { Palette } from '@bty/components'
import { Icon } from '@/components/base/icon'

const content = [
  {
    status: 'completed',
    html: `<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'Source Han Sans CN';
        src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
      }
      body {
        font-family: 'Source <PERSON> Sans CN', 'Noto Sans SC', sans-serif;
        margin: 0;
        padding: 0;
      }
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #1E88E5 0%, #43A047 100%);
        color: #FFFFFF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
      }
      .title {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 30px;
        text-align: center;
        line-height: 1.3;
      }
      .subtitle {
        font-size: 24px;
        margin-bottom: 60px;
        text-align: center;
      }
      .date {
        font-size: 20px;
        margin-top: 40px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="content-wrapper">
        <img src="https://private-us-east-1.manuscdn.com/sessionFile/VSouedY3GTFmI0gun5XpO7/sandbox/slides_resource_il2473rkd0c9nxavkujdx-3839b14f-e2b-prod-aws_1752677086951_na1fn_L2hvbWUvdWJ1bnR1L3VwbG9hZC9zZWFyY2hfaW1hZ2VzL3RnTnBUQ1EzNTR2Sg.jpg?x-oss-process=image/resize,w_1560,h_1560/format,webp&Expires=1798761600&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9wcml2YXRlLXVzLWVhc3QtMS5tYW51c2Nkbi5jb20vc2Vzc2lvbkZpbGUvVlNvdWVkWTNHVEZtSTBndW41WHBPNy9zYW5kYm94L3NsaWRlc19yZXNvdXJjZV9pbDI0NzNya2QwYzlueGF2a3VqZHgtMzgzOWIxNGYtZTJiLXByb2QtYXdzXzE3NTI2NzcwODY5NTFfbmExZm5fTDJodmJXVXZkV0oxYm5SMUwzVndiRzloWkM5elpXRnlZMmhmYVcxaFoyVnpMM1JuVG5CVVExRXpOVFIyU2cuanBnP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLHdfMTU2MCxoXzE1NjAvZm9ybWF0LHdlYnAiLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE3OTg3NjE2MDB9fX1dfQ__&Key-Pair-Id=K2HSFNDJXOU9YS&Signature=Rym8JAF4xn0w5t5kTU1ugWOnM3QS2wc2bs6fhbUi2mY4h4ZRhobUVPymcmnlmaDK7P2EOKTKLEKEL49-z2cwP-OSup4fl1N1cP9ZTEzWir-WeFQLUxLtHcZVaFJfgzs7equThz5kipxAXTXUI7Rz-pa67BVhG12228~jBQ5g0wxajrZTnB-Py1lPdX-AZfChfpCE6YAWeoXfg~Yt74gxgtljSyh6DFBSeuPMqgCmdCSaVFtQwrjnU7VuREaZjSqjF4D89sUgOCYvx6FJa50tDMTFDgEXvsmjEGiE3gi6~hnR2T1Sirr2xwbMxBXaFk~48Eq1MXDwJ1zrX0dqbLyy-Q__" alt="光伏产业" style="width: 600px; height: 300px; object-fit: cover; border-radius: 10px; margin-bottom: 40px;">
        <h1 class="title">A股光伏板块近期走势与政策影响分析</h1>
        <p class="subtitle">专业解读行业动态与未来发展趋势</p>
        <p class="date">2025年7月16日</p>
      </div>
    </div>
  </body>
</html>

`,
  },
  {
    status: 'running',
    html: `<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'Source Han Sans CN';
        src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
      }
      body {
        font-family: 'Source Han Sans CN', 'Noto Sans SC', sans-serif;
        margin: 0;
        padding: 0;
      }
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #1E88E5 0%, #43A047 100%);
        color: #FFFFFF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
      }
      .title {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 30px;
        text-align: center;
        line-height: 1.3;
      }
      .subtitle {
        font-size: 24px;
        margin-bottom: 60px;
        text-align: center;
      }
      .date {
        font-size: 20px;
        margin-top: 40px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="content-wrapper">
        <img src="https://private-us-east-1.manuscdn.com/sessionFile/VSouedY3GTFmI0gun5XpO7/sandbox/slides_resource_il2473rkd0c9nxavkujdx-3839b14f-e2b-prod-aws_1752677086951_na1fn_L2hvbWUvdWJ1bnR1L3VwbG9hZC9zZWFyY2hfaW1hZ2VzL3RnTnBUQ1EzNTR2Sg.jpg?x-oss-process=image/resize,w_1560,h_1560/format,webp&Expires=1798761600&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9wcml2YXRlLXVzLWVhc3QtMS5tYW51c2Nkbi5jb20vc2Vzc2lvbkZpbGUvVlNvdWVkWTNHVEZtSTBndW41WHBPNy9zYW5kYm94L3NsaWRlc19yZXNvdXJjZV9pbDI0NzNya2QwYzlueGF2a3VqZHgtMzgzOWIxNGYtZTJiLXByb2QtYXdzXzE3NTI2NzcwODY5NTFfbmExZm5fTDJodmJXVXZkV0oxYm5SMUwzVndiRzloWkM5elpXRnlZMmhmYVcxaFoyVnpMM1JuVG5CVVExRXpOVFIyU2cuanBnP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLHdfMTU2MCxoXzE1NjAvZm9ybWF0LHdlYnAiLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE3OTg3NjE2MDB9fX1dfQ__&Key-Pair-Id=K2HSFNDJXOU9YS&Signature=Rym8JAF4xn0w5t5kTU1ugWOnM3QS2wc2bs6fhbUi2mY4h4ZRhobUVPymcmnlmaDK7P2EOKTKLEKEL49-z2cwP-OSup4fl1N1cP9ZTEzWir-WeFQLUxLtHcZVaFJfgzs7equThz5kipxAXTXUI7Rz-pa67BVhG12228~jBQ5g0wxajrZTnB-Py1lPdX-AZfChfpCE6YAWeoXfg~Yt74gxgtljSyh6DFBSeuPMqgCmdCSaVFtQwrjnU7VuREaZjSqjF4D89sUgOCYvx6FJa50tDMTFDgEXvsmjEGiE3gi6~hnR2T1Sirr2xwbMxBXaFk~48Eq1MXDwJ1zrX0dqbLyy-Q__" alt="光伏产业" style="width: 600px; height: 300px; object-fit: cover; border-radius: 10px; margin-bottom: 40px;">
        <h1 class="title">A股光伏板块近期走势与政策影响分析</h1>
        <p class="subtitle">专业解读行业动态与未来发展趋势</p>
        <p class="date">2025年7月16日</p>
      </div>
    </div>
  </body>
</html>

`,
  },
  {
    status: 'waiting',
  },
]

export function Slides() {
  return content.map((page, index) => (
    <div
      key={index}
      className='w-full h-540px rounded-12px overflow-hidden bg-white border border-solid border-[rgba(225,225,229,0.6)] mb-20px'
    >
      {page.status === 'waiting' ? (
        <div className='flex flex-col h-full justify-center items-center'>
          <Icon className='i-icons-pptx-waiting' size='size-22px' />
          <p className='text-#8d8d99 text-14px/24px mt-8px'>等待中</p>
        </div>
      ) : page.status === 'running' ? (
        <Palette
          overrides={{ root: 'h-full px-24px py-16px overflow-auto' }}
          lang='html'
          code={page.html}
        />
      ) : page.status === 'completed' ? (
        <iframe
          className='w-full h-full border-none'
          srcDoc={page.html}
          sandbox='allow-scripts'
        />
      ) : null}
    </div>
  ))
}
