import { Preview } from '@bty/components'
import { useEffect, useState } from 'react'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTaskListStore } from '@/store/task-list'
import { Slides } from '../components/slides'
import { TaskLoading } from './task-loading'

interface TaskFileProps {
  type: string
  path?: string
  content?: string
}

export function TaskFile({ type, path, content }: TaskFileProps) {
  const taskId = useTaskListStore(s => s.taskId)

  const [url, setUrl] = useState<string>()

  useEffect(() => {
    if (path) {
      getFileUrl(taskId!, path).then(setUrl)
    }
  }, [taskId, path])

  if (!url && !content) {
    return <TaskLoading />
  }

  switch (type) {
    case 'csv':
      return (
        <Preview.CSV
          url={url}
          content={content}
          loadingIndicator={
            <TaskLoading className='h-full flex flex-col justify-center' />
          }
        />
      )
    case 'pptx':
      return <Slides />
    case 'txt':
      return <div>{content}</div>
    default:
      return (
        <p className='text-center text-#8d8d99 mt-100px'>
          此文件类型暂不支持预览
        </p>
      )
  }
}
