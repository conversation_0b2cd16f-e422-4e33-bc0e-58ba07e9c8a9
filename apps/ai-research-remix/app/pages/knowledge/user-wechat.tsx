import { memo, useMemo } from 'react'
import { Popover } from 'antd'
import { useMemoizedFn, useRequest } from 'ahooks'
import { delWechatBinding, getWechatBinding } from '@apis/mindnote'
import { Icon } from '@/components/base/icon'
import { Image } from '@/components/base/image'

export const UserWechat = memo(() => {
  const { data, run } = useRequest(getWechatBinding)

  const handleDelBind = useMemoizedFn(async () => {
    await delWechatBinding()
    run()
  })

  const popContent = useMemo(() => {
    return (
      <div className='text-center p-16px'>
        <div>微信关注 NovaTeam 收集助手</div>
        <div>转发短视频、公众号文章可以快速收集</div>
        <div className='flex-center py-14px'>
          <Image
            referrerPolicy='no-referrer'
            className='size-150px bg-#fff'
            src={data?.qr_code}
          />
        </div>
        {!data?.user ? (
          <div>请扫码绑定微信</div>
        ) : (
          <div>
            已绑定微信
            <span className='text-primary ml-4px'>{data.user.sender_name}</span>
            <span
              className='text-primary ml-4px cursor-pointer'
              onClick={handleDelBind}
            >
              解绑
            </span>
          </div>
        )}
      </div>
    )
  }, [data])

  return (
    <Popover
      overlayClassName='w-300px'
      overlayInnerStyle={{ borderRadius: 16 }}
      placement='top'
      arrow={false}
      content={popContent}
      onOpenChange={run}
    >
      <div className='flex-center self-stretch rd-8px cursor-pointer h-36px gap-6px m-16px hover:bg-[#E8E8E8] [&.ant-dropdown-open]:bg-[#E8E8E8] of-hidden'>
        <Icon icon='i-icons-wechat' />
        <span className='text-13px/24px font-500 text-nowrap'>微信助手</span>
      </div>
    </Popover>
  )
})
