import type { FormInstance } from 'antd'
import { Button, Form, Input, message } from 'antd'
import { useMemoizedFn, useRequest, useTitle } from 'ahooks'
import { cn } from '@bty/util'
import { betaUserApply } from '@apis/mindnote/user'
import { useState } from 'react'
import type { BetaUserApplyInfo } from '@apis/mindnote/user.type'
import { basePath } from '@/const'

interface ApplyTipsProps {
  className?: string
}
function ApplyTips({ className }: ApplyTipsProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center', className)}>
      <img
        src={`${basePath}/login-apply-success.svg`}
        className='size-140px'
        alt=''
      />
      <p className='c-#8D8D99 text-16px text-center'>
        已收到您的申请，请耐心等待内测权限开通
      </p>
    </div>
  )
}

export function RequiredMark() {
  return (
    <span
      className={cn(
        'color-error ml-5px text-18px line-height-14px position-relative top-3px',
      )}
    >
      *
    </span>
  )
}

interface ApplyFormProps {
  form: FormInstance
  onSubmit: (values: any) => Promise<void>
}

function ApplyForm({ form, onSubmit }: ApplyFormProps) {
  const handleSubmit = useMemoizedFn(async (values: any) => {
    await onSubmit(values)
  })

  return (
    <div>
      <p className='c-#8D8D99 text-16px text-center'>
        提交申请，我们将尽快为您开通内测权限
      </p>

      <Form
        autoComplete='new-password'
        className='!mt-24px [&_.ant-form-item-explain]:text-12px!'
        requiredMark={false}
        method='post'
        layout='vertical'
        form={form}
        onFinish={handleSubmit}
      >
        <Form.Item
          label={
            <div>
              姓名
              <RequiredMark />
            </div>
          }
          name='name'
          rules={[
            {
              required: true,
              message: '请输入姓名',
            },
          ]}
        >
          <Input variant='filled' placeholder='怎么称呼你' />
        </Form.Item>

        <Form.Item label='职业' name='profession'>
          <Input variant='filled' placeholder='你的职业' />
        </Form.Item>

        <Form.Item label='你的需求' name='demand'>
          <Input.TextArea
            variant='filled'
            placeholder='你的需求，越详细越容易通过哦'
            rows={2}
          />
        </Form.Item>

        <Form.Item noStyle>
          <Button className='mt-34px' type='primary' htmlType='submit' block>
            提交申请
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}

interface SignApplyProps {
  className?: string
  isApplying?: boolean
}

export function SignApply({ isApplying = false }: SignApplyProps) {
  useTitle('申请内测')

  const [form] = Form.useForm()
  const [showApplyTips, setShowApplyTips] = useState(isApplying)

  const { runAsync: submitApply } = useRequest(betaUserApply, {
    manual: true,
    onSuccess: () => {
      message.success('申请提交成功')
      setShowApplyTips(true)
    },
  })

  const handleSubmit = useMemoizedFn(async (values: BetaUserApplyInfo) => {
    await submitApply({
      ...values,
      status: 'UnderReview',
    })
  })

  return (
    <div className='flex-none w-426px px-60px relative'>
      <div className='text-28px/32px text-center font-600 mt-46px mb-24px'>
        <div>NovaTeam 正在内测中</div>
        <div className='mt-8px'>欢迎参与体验</div>
      </div>

      {showApplyTips ? (
        <ApplyTips className='mt-80px' />
      ) : (
        <ApplyForm form={form} onSubmit={handleSubmit} />
      )}
    </div>
  )
}
