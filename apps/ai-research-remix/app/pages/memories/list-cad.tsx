import type { Memory } from '@apis/mindnote/memories'
import { cn } from '@bty/util'
import { memo, useRef } from 'react'
import dayjs from 'dayjs'
import { useFadeIn } from '@/hooks/use-fade-in'
import { useMemories } from '@/store/memories'

interface MemorySummaryProps
  extends Pick<Memory, 'id' | 'name' | 'describe' | 'updated_at'> {
  index: number
  selected?: boolean
}

export const MemoryCard = memo((props: MemorySummaryProps) => {
  const { id, name, describe, updated_at, index, selected } = props
  const setMemoryId = useMemories(state => state.setMemoryId)

  const ref = useRef<HTMLDivElement>(null)

  useFadeIn(ref, {
    delay: Math.min((index % 10) * 100, 1500),
    fill: 'forwards',
  })

  return (
    <div
      ref={ref}
      className={cn(
        'mb-8px last:mb-0px bg-white rounded-8px b-1px b-solid b-#E1E1E5/60 transition-colors overflow-hidden cursor-pointer',
        {
          'b-#7b61ff! bg-#7B61FF/8': selected,
        },
      )}
      onClick={() => setMemoryId(id)}
    >
      <div className='p-16px'>
        <p className='text-truncate text-16px/16px mb-8px'>{name}</p>
        <p className='text-truncate text-14px/20px text-#8d8d99'>{describe}</p>
      </div>
      <div className='flex items-center bg-#626999/3 h-28px px-16px text-12px/16px text-#8d8d99/80'>
        <span className='ml-auto'>
          {dayjs(updated_at).format('YYYY-MM-DD HH:mm')}
        </span>
      </div>
    </div>
  )
})
