import { memo, useEffect, useRef, useState } from 'react'
import { getMemory } from '@apis/mindnote/memories'
import { App, Dropdown, Input } from 'antd'
import { useMemoizedFn, useThrottleFn } from 'ahooks'
import dayjs from 'dayjs'
import type { IMERef } from '@bty/components/editor/markdown-editor'
import { MarkdownEditor } from '@bty/components/editor/markdown-editor'
import { DRAFT_MEMORY_ID, useMemories } from '@/store/memories'
import { Icon, IconButton } from '@/components/base/icon'
import { CONFIRM_CONFIG } from '@/components/base/modal-config'
import { ScrollView } from '@/components/base/scroll-view'

const menu = [
  {
    key: 'delete',
    label: (
      <div className='w-80px flex items-center gap-4px text-error'>
        <Icon icon='i-icons-delete' />
        删除
      </div>
    ),
  },
]

const TYPE_ICON: Record<string, any> = {
  paragraph: <Icon icon='i-icons-md-paragraph' />,
  head1: <Icon icon='i-icons-md-h1' />,
  head2: <Icon icon='i-icons-md-h2' />,
  head3: <Icon icon='i-icons-md-h3' />,
  bulletList: <Icon icon='i-icons-md-list' />,
}

export const MemoryDetail = memo(() => {
  const { modal } = App.useApp()

  const isComposing = useRef(false)
  const memoryId = useMemories(state => state.memoryId)
  const updateMemory = useMemories(state => state.update)
  const removeMemory = useMemories(state => state.remove)

  const editorRef = useRef<IMERef>()
  const contentRef = useRef('')
  const [updateTime, setUpdateTime] = useState('')
  const [title, setTitle] = useState('')

  const handleMenu = useMemoizedFn((info: any) => {
    if (info.key === 'delete') {
      modal.confirm({
        ...CONFIRM_CONFIG,
        content: (
          <div className='mb-34px text-14px/22px font-500'>
            是否确认删除内容？
          </div>
        ),
        onOk: () => {
          removeMemory()
        },
      })
    }
  })

  const { run: handleSave } = useThrottleFn(
    (title: string) => {
      updateMemory({
        name: title,
        task_structure: [
          {
            step: 1,
            name: '',
            experience: contentRef.current,
          },
        ],
      })
      setUpdateTime(dayjs().format('YYYY-MM-DD HH:mm'))
    },
    { wait: 1000 },
  )

  const handleTitleChange = useMemoizedFn(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTitle(e.target.value)
      if (isComposing.current) return
      handleSave(e.target.value)
    },
  )

  const handleKeyDown = useMemoizedFn(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        editorRef.current?.focus()
      }
    },
  )

  const handleContentChange = useMemoizedFn((value: string) => {
    contentRef.current = value
    handleSave(title)
  })

  useEffect(() => {
    if (!memoryId || memoryId.startsWith(DRAFT_MEMORY_ID)) {
      setTitle('')
      editorRef.current?.changeValue('')
      setUpdateTime(dayjs().format('YYYY-MM-DD HH:mm'))
      return
    }

    getMemory(memoryId).then(res => {
      setTitle(res.name)
      editorRef.current?.changeValue(res.task_structure?.[0]?.experience || '')
      setUpdateTime(res.updated_at)
    })
  }, [memoryId])

  return (
    <div className='flex-1 h-full of-hidden flex flex-col items-stretch'>
      <header className='w-full px-24px pt-16px pb-4px flex-center gap-12px'>
        <span className='ml-auto c-#8D8D99/80'>
          {dayjs(updateTime).format('YYYY-MM-DD HH:mm')}
        </span>
        <Dropdown
          placement='bottomRight'
          menu={{ items: menu, onClick: handleMenu }}
        >
          <IconButton
            icon='i-icons-more'
            iconSize='size-18px'
            size='size-36px'
            className='rd-8px!'
          />
        </Dropdown>
      </header>

      <Input
        name='title'
        variant='borderless'
        className='text-22px font-500 max-w-720px px-20px m-auto'
        placeholder='请输入标题'
        value={title}
        onChange={handleTitleChange}
        onKeyDown={handleKeyDown}
        onCompositionStart={() => (isComposing.current = true)}
        onCompositionEnd={e => {
          isComposing.current = false
          handleTitleChange(e as any)
        }}
      />

      <ScrollView
        className='flex-1 cursor-text'
        onClick={() => editorRef.current?.focus()}
      >
        <div className='m-auto max-w-720px p-20px pt-0px flex-1'>
          <MarkdownEditor
            eRef={editorRef}
            className='text-14px'
            placeholder='请输入内容'
            typeIcon={TYPE_ICON}
            onChange={handleContentChange}
          />
        </div>
      </ScrollView>
    </div>
  )
})
