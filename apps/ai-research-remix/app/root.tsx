import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@remix-run/react'
import type { LinksFunction } from '@remix-run/node'
import { Empty } from './components/base/empty'
import { basePath, HOME_PATH } from './const'
import { ClientRoot } from './root-client'

export const links: LinksFunction = () => [
  {
    rel: 'icon',
    href: `${basePath}/logo.png`,
  },
]

export function loader() {
  return Response.json({
    envVars: {
      VITE_OSS_RESOURCE_BUCKET: process.env.VITE_OSS_RESOURCE_BUCKET,
    },
  })
}

export function Layout({ children }: { children: React.ReactNode }) {
  if (import.meta.env.SSR) {
    return (
      <html lang='zh'>
        <head>
          <meta charSet='utf-8' />
          <meta name='viewport' content='width=device-width, initial-scale=1' />
          <meta property='og:title' content='NovaTeam' />
          <meta property='og:site_name' content='NovaTeam' />
          <meta property='og:url' content='https://ai.aimindnote.com' />
          <meta
            property='og:image'
            content='https://ai.aimindnote.com/logo-bg.png'
          />
          <meta
            property='og:description'
            content='NovaTeam 你的专属Agent，越用越专业'
          />

          <meta name='theme-color' content='#fff' />
          <script src={`${basePath}/js/AestheticFluidBg.min.js`} />
          <Meta />
          <Links />
        </head>
        <body>
          <div id='app'>
            <Scripts />
          </div>
        </body>
      </html>
    )
  }

  return (
    <>
      <Links />
      <Scripts />
      {children}
    </>
  )
}

export function ErrorBoundary() {
  return (
    <>
      <Links />
      <Scripts />
      <Empty
        image={`${basePath}/404.png`}
        imageClassname='w-200px'
        desc={
          <>
            <div>抱歉！您访问的页面不存在</div>

            <div className='mt-10px'>
              <a className='decoration-none text-primary' href={HOME_PATH}>
                回到首页
              </a>
            </div>
          </>
        }
      />
    </>
  )
}

export default function App() {
  if (import.meta.env.SSR) {
    return <Outlet />
  }

  return (
    <ClientRoot>
      <Outlet />
    </ClientRoot>
  )
}
