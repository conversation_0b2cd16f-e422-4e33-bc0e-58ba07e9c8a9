import { create } from 'zustand'
import type { TeamListItem } from '@apis/mindnote/team'
import { getUserTeamList } from '@apis/mindnote/team'
import { useMemo } from 'react'

interface TeamStore {
  teamList: TeamListItem[] & { theme?: Record<string, any> }
  init: () => Promise<void>
}

const defaultThemeColorList = [
  {
    starter:
      'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #D6EEFF 7%, rgba(214, 238, 255, 0) 95%), #F7F7FA',
    chat: 'linear-gradient(180deg, rgba(235, 247, 255, 0.9) 7%, rgba(235, 247, 255, 0) 92%), #F7F7FA',
    sandbox:
      'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(155, 213, 255, 0.4) 0%, #9BD5FF 7%), #FFFFFF',
  },
  {
    starter:
      'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #FFDBED 7%, rgba(255, 219, 237, 0) 95%), #F7F7FA',
    chat: 'linear-gradient(180deg, rgba(255, 240, 247, 0.9) 7%, rgba(255, 240, 247, 0) 92%), #F7F7FA',
    sandbox:
      'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(255, 174, 215, 0.4) 0%, #FFAED7 7%), #FFFFFF',
  },
  {
    starter:
      'linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, #EDD6FF 7%, rgba(237, 214, 255, 0) 95%), #F7F7FA',
    chat: 'linear-gradient(180deg, rgba(246, 235, 255, 0.89) 7%, rgba(246, 235, 255, 0) 92%), #F7F7FA',
    sandbox:
      'linear-gradient(270deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 72%), linear-gradient(180deg, rgba(221, 179, 255, 0.4) 0%, #DDB3FF 7%), #FFFFFF',
  },
]

export const useTeamInfoStore = /* @__PURE__ */ create<TeamStore>(
  (set, _get) => ({
    teamList: [],
    init: async () => {
      const res = await getUserTeamList()
      set(() => ({ teamList: res }))
    },
  }),
)

export function useTeamTheme(teamId: string) {
  const { teamList } = useTeamInfoStore()

  const theme = useMemo(() => {
    const idx = teamList?.findIndex(each => each.team_id === teamId)
    if (idx > 0) {
      return defaultThemeColorList[idx]
    }
    return defaultThemeColorList[0]
  }, [teamId, teamList])

  return theme!
}

export function useThemeInfo(teamId: string) {
  const { teamList } = useTeamInfoStore()

  const team = useMemo(() => {
    const item = teamList?.find(each => each.team_id === teamId)
    return item!
  }, [teamId, teamList])

  return team
}
