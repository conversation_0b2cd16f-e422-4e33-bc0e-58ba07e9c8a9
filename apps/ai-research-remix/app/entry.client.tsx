import { RemixBrowser } from '@remix-run/react'
import { createRoot } from 'react-dom/client'
import { injectHttp } from './module/http.client'
import { useMcpStore } from './store/mcp'
import { isSharePage } from './util/is-share-page'
import { useTeamInfoStore } from './store/team'

const dom = document.getElementById('app')
const root = createRoot(dom!)
root.render(<RemixBrowser />)

injectHttp()
useTeamInfoStore.getState().init()
if (!isSharePage()) {
  useMcpStore.getState().init()
}

if (process.env.NODE_ENV !== 'development') {
  import('@microsoft/clarity').then(module => {
    module.default.init('qrmn5lxa6d')
  })

  import('@bty/monitor').then(module => {
    module.monitor.init({
      pid: 'ce0ak81asd@10428884a6e93ee',
      env: module.mapToMonitorEnvironment(process.env.NODE_ENV!),
    })
  })
}
