import { redirect } from '@remix-run/node'
import type { LoaderFunctionArgs } from '@remix-run/node'
import type { MetaFunction } from '@remix-run/react'
import { Outlet, useMatch } from '@remix-run/react'
import { memo, useEffect, useMemo } from 'react'
import { checkUserCanUse } from '@apis/mindnote/user'
import { MainNav } from '@/pages/main/nav'
import { User } from '@/pages/main/user'
import { APPLY_STATUS_CODE, basePath, LOGIN_PATH } from '@/const'
import { Icon } from '@/components/base/icon'
import { Image } from '@/components/base/image'
import { UserDownload } from '@/pages/main/user-download'
import { useReadStore } from '@/store/read'

export const meta: MetaFunction = () => {
  return [{ title: 'NovaTeam' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  const res = await checkUserCanUse(context.userToken)

  if (
    [APPLY_STATUS_CODE.NO_PERMISSION, APPLY_STATUS_CODE.APPLYING].includes(
      res?.code,
    )
  ) {
    return redirect(LOGIN_PATH)
  }

  return {}
}
interface IconProps {
  active?: boolean
}

function TaskIcon(props: IconProps) {
  return (
    <Icon
      icon={props.active ? 'i-icons-nav-task-active' : 'i-icons-nav-task'}
      size='size-18px'
    />
  )
}
function TeamIcon(props: IconProps) {
  return (
    <Icon
      icon={props.active ? 'i-icons-nav-team-active' : 'i-icons-nav-team'}
      size='size-18px'
    />
  )
}
function MemoriesIcon(props: IconProps) {
  return (
    <Icon
      icon={props.active ? 'i-icons-nav-memory-active' : 'i-icons-nav-memory'}
      size='size-18px'
    />
  )
}
function KnowledgeIcon(props: IconProps) {
  return (
    <Icon
      icon={
        props.active ? 'i-icons-nav-knowledge-active' : 'i-icons-nav-knowledge'
      }
      size='size-18px'
    />
  )
}
// function AskIcon(props: IconProps) {
//   return (
//     <Icon
//       icon='i-icons-nav-ai-ask'
//       size='size-18px'
//       className={props.active ? 'text-[#7B61FF]' : 'text-#8D8D99'}
//     />
//   )
// }

// function ToolsIcon(props: IconProps) {
//   return (
//     <Icon
//       icon='i-icons-nav-tools'
//       size='size-18px'
//       className={props.active ? 'text-[#A061FF]' : 'text-#8D8D99'}
//     />
//   )
// }
// function MCPIcon(props: IconProps) {
//   return (
//     <Icon
//       icon='i-icons-nav-mcp'
//       size='size-18px'
//       className={props.active ? 'text-[#A061FF]' : 'text-#8D8D99'}
//     />
//   )
// }

function Main() {
  const mainMatch = useMatch('/main/:type/*')

  const mainType = useMemo(
    () => mainMatch?.params.type ?? '',
    [mainMatch?.params.type],
  )

  const allRead = useReadStore(s => s.allRead)

  useEffect(() => {
    document.dispatchEvent(new Event('updateTagList'))
    document.dispatchEvent(new Event('updateReadState'))
  }, [])

  return (
    <main className='main-page size-full flex bg-#F3F3F5'>
      <div className='h-full w-72px flex-none flex flex-col items-center relative'>
        <div className='w-full flex-none pt-16px flex-center'>
          <Image
            className='flex-none h-32px m-2px'
            src={`${basePath}/logo.svg`}
          />
        </div>

        <div className='w-full flex-none flex flex-col gap-4px p-12px'>
          <MainNav
            now={mainType}
            router='next-agent'
            icon={<TaskIcon active={mainType === 'next-agent'} />}
            name='任务'
          />
          <MainNav
            now={mainType}
            router='team'
            icon={<TeamIcon active={mainType === 'team'} />}
            name='Agents'
          />
          <MainNav
            now={mainType}
            router='memories'
            icon={<MemoriesIcon active={mainType === 'memories'} />}
            name='经验'
          />
          <MainNav
            now={mainType}
            router='knowledge'
            icon={<KnowledgeIcon active={mainType === 'knowledge'} />}
            name='知识'
            extra={
              !allRead.collection || !allRead.subscribe ? (
                <div className='top-4px right-4px size-8px bg-[#7B61FF] b-line b-1 b-#Fff rd-6px absolute' />
              ) : null
            }
          />

          {/* <MainNav
            now={mainType}
            router='ask'
            icon={<AskIcon active={mainType === 'ask'} />}
            name='AI Ask'
          /> */}
        </div>

        <div className='mt-auto w-full flex-none p-12px flex-center flex-col gap-8px dis'>
          {/* <MainNav
              now={mainType}
              router='mcp-chat'
              icon={<ToolsIcon active={mainType === 'mcp-chat'} />}
              name='MChat'
            />
            <MainNav
              now={mainType}
              router='mcp'
              icon={<MCPIcon active={mainType === 'mcp'} />}
              name='MCP'
            /> */}
          {window.MindNote?.platform !== 'macOS' && <UserDownload />}
          <User />
        </div>
      </div>

      <div className='flex-1 overflow-hidden'>
        <Outlet />
      </div>
    </main>
  )
}

export default !import.meta.env.SSR ? memo(Main) : () => null
