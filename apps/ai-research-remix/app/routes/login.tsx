/* eslint-disable no-new */
import { redirect, useLoaderData } from '@remix-run/react'
import type { MetaFunction } from '@remix-run/react'
import { useBoolean, useTitle, useMemoizedFn } from 'ahooks'
import { memo, useEffect, useState } from 'react'
import classNames from 'classnames'
import { cn } from '@bty/util'
import type { LoaderFunctionArgs } from '@remix-run/node'
import { checkUserCanUse } from '@apis/mindnote/user'
import { SignIn } from '@/pages/sign/sign-in'
import { SignForget } from '@/pages/sign/sign-forget'
import { SignApply } from '@/pages/sign/sign-apply'
import { APPLY_STATUS_CODE, HOME_PATH, TOKEN_NAME } from '@/const'
import { useTaskListStore } from '@/store/task-list'

export const meta: MetaFunction = () => {
  return [{ title: 'NovaTeam 你的专属Agent，越用越专业' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  if (!context.userToken) {
    return {
      status: 'Default',
    }
  }
  const res = await checkUserCanUse(context.userToken)

  if (
    context.user?.userId &&
    ![APPLY_STATUS_CODE.NO_PERMISSION, APPLY_STATUS_CODE.APPLYING].includes(
      res?.code,
    )
  ) {
    return redirect(HOME_PATH)
  }
  return {
    status: res?.code === APPLY_STATUS_CODE.APPLYING ? 'Applying' : 'Default',
  }
}

function Login() {
  useTitle('NovaTeam 你的专属Agent，越用越专业')

  const { status } = useLoaderData<typeof loader>()
  const reset = useTaskListStore(s => s.reset)

  const [isForget, { setTrue: openForget, setFalse: closeForget }] =
    useBoolean(false)
  const [phone, setPhone] = useState('')
  const [resetId, setResetId] = useState<number>()

  const handleForget = useMemoizedFn((phone?: string) => {
    setPhone(phone ?? '')
    openForget()
  })

  const handleReset = useMemoizedFn(() => {
    setResetId(Date.now())
    closeForget()
  })

  const [isApply, { setTrue: openApply, setFalse: closeApply }] = useBoolean(
    status === 'Applying',
  )

  const [isApplying, { set: setApplying }] = useBoolean(status === 'Applying')

  useEffect(() => {
    reset()
    try {
      new window.Color4Bg.AestheticFluidBg({
        dom: 'login-bg',
        colors: [
          '#ffc2d4',
          '#66b0ff',
          '#8a9dff',
          '#ffffff',
          '#738af7',
          '#bdeeff',
        ],
        loop: true,
      })
    } catch (error) {
      console.error('Error initializing background:', error)
    }
  }, [])

  const onApply = (isApplying: boolean) => {
    openApply()
    isApplying && setApplying(!!isApplying)
  }

  const handleLogout = () => {
    document.cookie = `${TOKEN_NAME}=; Path=/; SameSite=Lax; Max-Age=0`
    closeApply()
  }

  return (
    <div className='mind-login relative w-screen h-screen flex justify-center items-center'>
      <div className='absolute inset-0px' id='login-bg'></div>
      <div className='flex flex-col items-center gap-4'>
        <div className='bg-white/80 backdrop-blur-10px rd-20px w-426px of-hidden'>
          <div
            className={classNames('flex duration-300', {
              'translate-x-[-426px]': isForget || isApply,
            })}
          >
            <SignIn
              onForget={handleForget}
              onApply={onApply}
              resetId={resetId}
            />
            {isApply && <SignApply isApplying={isApplying} />}
            {isForget && (
              <SignForget
                reset={isForget}
                phone={phone}
                onBack={closeForget}
                onSuccess={handleReset}
              />
            )}
          </div>
        </div>
        <div
          className={cn(
            'text-primary opacity-0 text-center cursor-pointer hover:text-primary/80 z-2',
            {
              'opacity-[1]': isApply,
            },
          )}
          onClick={handleLogout}
          onKeyDown={e => e.key === 'Enter' && handleLogout()}
          role='button'
          tabIndex={0}
        >
          退出登录
        </div>
      </div>

      <div className='absolute bottom-10px w-full text-center text-12px'>
        <span className='cursor-pointer mr-8px'>
          Copyright©2025 斑头雁(杭州)智能科技有限责任公司
        </span>

        <a
          className='hover:text-#000 hover:underline'
          href='https://beian.miit.gov.cn'
          target='_blank'
          rel='noreferrer'
        >
          浙ICP备2022000025号
        </a>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(Login) : () => null
