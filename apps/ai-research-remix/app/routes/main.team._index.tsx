import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { useNavigate } from '@remix-run/react'
import { memo } from 'react'
import { useRequest } from 'ahooks'
import { message } from 'antd'
import { getUserTeamList, type TeamListItem } from '@apis/mindnote/team'

export const meta: MetaFunction = () => {
  return [{ title: '团队' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return {
    user: context.user,
    token: context.userToken as string,
  }
}

// 公共字体样式
const baseTextStyle = {
  fontFamily: 'PingFang SC',
  fontVariationSettings: '"opsz" auto',
  letterSpacing: '0px',
}

// 团队卡片尺寸
const CARD_DIMENSIONS = {
  width: '360px',
  height: '480px',
} as const

// 团队名称样式
const teamNameStyle = {
  ...baseTextStyle,
  fontSize: '40px',
  fontWeight: 500,
  lineHeight: '48px',
  color: '#17171D',
} as const

// 团队描述样式
const teamDescriptionStyle = {
  ...baseTextStyle,
  fontSize: '16px',
  lineHeight: '20px',
  color: '#8D8D99',
} as const

// 页面标题样式
const pageTitleStyle = {
  ...baseTextStyle,
  fontSize: '20px',
  fontWeight: 500,
  lineHeight: '20px',
  color: '#17171D',
} as const

// Main component
function TeamIndexPage() {
  // 将API团队数据适配为显示格式
  const adaptTeamForDisplay = (team: TeamListItem) => ({
    id: team.team_id,
    name: team.team_name,
    description: team.team_desc || '暂无描述',
    background: team.background,
  })

  // 使用API获取团队列表
  const { data: teamListResponse } = useRequest(getUserTeamList, {
    onError: err => {
      message.error(`获取团队列表失败，请稍后重试，error: ${err}`)
    },
  })

  // 处理API返回的团队数据
  const teams = teamListResponse || []

  const adaptedTeams = teams.map(adaptTeamForDisplay)

  // Team Card Component
  const TeamCard = memo<{ team: ReturnType<typeof adaptTeamForDisplay> }>(
    ({ team }) => {
      const navigate = useNavigate()

      const handleCardClick = () => {
        navigate(`/main/team/${team.id}`)
      }

      return (
        <div
          className='group relative transition-all duration-300 hover:-translate-y-1 flex flex-col items-center cursor-pointer'
          style={CARD_DIMENSIONS}
          onClick={handleCardClick}
        >
          {/* Card container */}
          <div className='relative w-full h-full bg-white shadow-sm hover:shadow-xl transition-shadow duration-300 overflow-hidden rounded-[24px] flex flex-col items-center'>
            {/* Image section */}
            <img
              src={team.background || ''}
              alt={team.name}
              className='w-full h-full object-cover rounded-t-[24px]'
              style={{ objectPosition: 'top', height: '100%' }}
            />

            {/* Arrow module - visible on hover */}
            <div className='absolute top-[24px] right-[24px] opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
              <div className='w-32px h-32px bg-white/60 backdrop-blur-sm rd-8px shadow-sm justify-center flex items-center'>
                <div className='i-icons-arrow-right w-24px h-24px text-[#17171D]'></div>
              </div>
            </div>

            {/* Team name */}
            <div
              className='absolute left-[30px] bottom-[60px]'
              style={teamNameStyle}
            >
              {team.name}
            </div>

            {/* Description text */}
            <div
              className='absolute left-[30px] bottom-[30px]'
              style={teamDescriptionStyle}
            >
              {team.description}
            </div>
          </div>
        </div>
      )
    },
  )

  return (
    <div className='size-full relative overflow-hidden bg-white rounded-[24px] flex flex-col'>
      {/* 背景层 */}
      <div
        className='absolute inset-0 rounded-[24px] z-0'
        style={{
          background:
            'linear-gradient(180deg, #FFFFFF 11%, #FFFFFF 11%, rgba(255, 255, 255, 0) 61%), linear-gradient(282deg, rgba(184, 218, 255, 0.7) 0%, rgba(216, 237, 254, 0.7) 38%, rgba(232, 251, 255, 0.7) 73%), #FFFFFF',
        }}
      />
      {/* 内容层 */}
      <div className='relative z-10 flex-1 flex flex-col px-6 py-10'>
        {/* 标题 */}
        <div style={pageTitleStyle}>团队</div>
        {/* 居中包裹层 */}
        <div className='flex flex-1 items-center justify-center gap-[50px]'>
          {adaptedTeams?.map(team => <TeamCard key={team.id} team={team} />)}
        </div>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(TeamIndexPage) : () => null
