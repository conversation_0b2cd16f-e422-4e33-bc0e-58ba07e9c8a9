import { redirect } from '@remix-run/node'
import type { LoaderFunctionArgs } from '@remix-run/node'
import type { MetaFunction } from '@remix-run/react'
import { Outlet, useMatch, useNavigate } from '@remix-run/react'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { useDebounceFn, useMemoizedFn } from 'ahooks'
import { App, Popover } from 'antd'
import { checkUserCanUse } from '@apis/mindnote/user'
import classNames from 'classnames'
import { KnowledgeNav, TagItemNav, TagNav } from '@/pages/knowledge/nav'
import { APPLY_STATUS_CODE, LOGIN_PATH } from '@/const'
import { useTagStore } from '@/store/tag'
import { useReadStore } from '@/store/read'
import { ScrollView } from '@/components/base/scroll-view'
import { useNavStore } from '@/store/nav'
import { IconButton } from '@/components/base/icon'
import { UserWechat } from '@/pages/knowledge/user-wechat'
import { GapBar } from '@/components/base/gap-bar'

export const meta: MetaFunction = () => {
  return [{ title: 'NovaTeam' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  const res = await checkUserCanUse(context.userToken)

  if (
    [APPLY_STATUS_CODE.NO_PERMISSION, APPLY_STATUS_CODE.APPLYING].includes(
      res?.code,
    )
  ) {
    return redirect(LOGIN_PATH)
  }

  return {}
}

const UNREAD_LOAD_COUNT = 2

function Knowledge() {
  const { message } = App.useApp()

  const navigate = useNavigate()
  const matchInfo = useMatch('/main/knowledge/:tagId')
  const mainType = useMemo(
    () => matchInfo?.params.tagId ?? '',
    [matchInfo?.params.tagId],
  )

  const showNav = useNavStore(s => s.show)
  const allRead = useReadStore(s => s.allRead)
  const readCount = useReadStore(s => s.readCount)
  const tagList = useTagStore(s => s.list)
  const unreadCount = useRef(0)
  const [showPop, setShowPop] = useState(false)

  const handleShowPopOpen = useMemoizedFn((open: boolean) => {
    if (showNav) return
    setShowPop(open)
  })

  const toggleShow = useMemoizedFn(() => {
    document.dispatchEvent(new Event(showNav ? 'closeNav' : 'showNav'))
    setShowPop(false)
  })

  const handleToTop = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.collection-list-container .scroll-view',
    )!
    if (!container) return
    container.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  })

  const handleToCollectionUnread = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.collection-list-container .scroll-view',
    )!
    if (!container) return
    if (allRead.collection) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      return
    }

    const card: HTMLElement = document.querySelector('.collection-card-unread')!
    if (card) {
      container.scrollTo({
        top: card.offsetTop - 40,
        behavior: 'smooth',
      })
      return
    }
    container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' })
  })

  const handleToSubUnread = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.sub-list-container .scroll-view',
    )!
    if (!container) return
    if (allRead.subscribe) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      return
    }

    const card: HTMLElement = document.querySelector('.sub-card-unread')!
    if (card) {
      unreadCount.current = 0
      container.scrollTo({
        top: card.offsetTop - 40,
        behavior: 'smooth',
      })
      return
    }
    if (unreadCount.current < UNREAD_LOAD_COUNT) {
      unreadCount.current += 1
      container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' })
      return
    }

    unreadCount.current = 0
    document.dispatchEvent(new Event('switchToUnread'))
    message.info('检测到大量未读，正在加载所有未读……')
  })

  const { run: handleRefresh } = useDebounceFn(
    () => {
      document.dispatchEvent(new Event('updateReadState'))
    },
    { wait: 1000 },
  )

  const handleTagDelete = useMemoizedFn((id: string) => {
    if (id !== mainType) return

    const list = useTagStore.getState().list
    if (list.length === 0) {
      navigate('/main/knowledge/collection')
    } else {
      navigate(`/main/knowledge/${list[0].label_id}`)
    }
  })

  const handleTagAdd = useMemoizedFn((id: string) => {
    navigate(`/main/knowledge/${id}`)
  })

  useEffect(() => {
    document.dispatchEvent(new Event('updateTagList'))
    document.dispatchEvent(new Event('updateReadState'))
  }, [])

  useEffect(() => {
    if (mainType) return
    navigate('/main/knowledge/collection')
  }, [mainType])

  const knowledgeNav = (
    <>
      <div className='w-full flex-none flex flex-col gap-4px p-12px'>
        <KnowledgeNav
          now={mainType}
          router='collection'
          name='收集'
          right={
            !allRead.collection ? (
              <div className='top-20px right-10px size-8px bg-[#7B61FF] b-line b-1 b-#Fff rd-6px absolute' />
            ) : (
              <span className='text-12px/26px text-#8D8D99/60'>
                {readCount.collection}
              </span>
            )
          }
          onClick={handleRefresh}
          onDoubleClick={handleToCollectionUnread}
        />

        <KnowledgeNav
          now={mainType}
          router='sub'
          name='订阅'
          right={
            !allRead.subscribe ? (
              <div className='top-20px right-10px size-8px bg-[#7B61FF] b-line b-1 b-#Fff rd-6px absolute' />
            ) : (
              <span className='text-12px/26px text-#8D8D99/60'>
                {readCount.subscribe}
              </span>
            )
          }
          onClick={handleRefresh}
          onDoubleClick={handleToSubUnread}
        />
      </div>

      <div className='h-1px mx-12px flex-none w-auto bg-[#E1E1E5]/80 self-stretch'></div>

      <div className='w-full flex-auto of-hidden flex flex-col gap-4px p-12px'>
        <TagNav showAdd={showNav} onAdd={handleTagAdd} />

        <ScrollView vbarClassName='right-[-12px]!'>
          {tagList.map(each => {
            return (
              <TagItemNav
                key={each.label_id}
                now={mainType}
                data={each}
                onDelete={handleTagDelete}
                onDoubleClick={handleToTop}
              />
            )
          })}
        </ScrollView>
      </div>
    </>
  )

  return (
    <div className='size-full flex relative'>
      <Popover
        placement='bottomLeft'
        trigger='hover'
        arrow={false}
        content={knowledgeNav}
        open={showPop}
        onOpenChange={handleShowPopOpen}
        overlayInnerStyle={{
          padding: 0,
          width: 212,
        }}
      >
        <IconButton
          icon='i-icons-collapse'
          iconSize='size-14px'
          size='size-36px'
          className={classNames(
            'text-[#626999]/60 flex-none absolute top-16px z-1000',
            {
              'left-162px rotate-180': showNav,
              'left-16px': !showNav,
            },
          )}
          onClick={toggleShow}
        />
      </Popover>

      <div
        className={classNames('h-full duration-300 of-hidden', {
          'w-0px! opacity-0': !showNav,
          'w-212px! opacity-100': showNav,
        })}
      >
        <div
          className={classNames(
            'h-full w-212px bg-#F7F7FA rd-24px flex-none flex flex-col items-center',
          )}
        >
          <div className='w-full px-24px pt-16px flex-center'>
            <span className='mr-auto text-20px/36px font-500'>知识</span>
          </div>
          {knowledgeNav}

          <UserWechat />
        </div>
      </div>

      {showNav && <GapBar />}

      <div className='flex-1 bg-white rd-24px of-hidden bg-#F7F7FA/60 b-solid b-1 b-#E1E1E5/80 of-hidden'>
        <Outlet />
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(Knowledge) : () => null
