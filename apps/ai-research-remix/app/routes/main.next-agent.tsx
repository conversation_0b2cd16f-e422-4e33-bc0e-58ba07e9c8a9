import { json } from '@remix-run/node'
import type { MetaFunction, LoaderFunctionArgs } from '@remix-run/node'
import { memo } from 'react'
import { useTitle } from 'ahooks'
import NextAgentPage from '@/pages/next-agent/page'

export const meta: MetaFunction = () => {
  return [{ title: '任务' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function NextAgent() {
  useTitle('任务')

  return <NextAgentPage isShareMode={false} />
}

export default !import.meta.env.SSR ? memo(NextAgent) : () => null
