import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTitle } from 'ahooks'
import { memo, useEffect } from 'react'
import { useMemories } from '@/store/memories'
import { Image } from '@/components/base/image'
import { basePath } from '@/const'
import { MemoriesEmpty } from '@/pages/memories/empty'
import { MemoryList } from '@/pages/memories/list'
import { MemoryDetail } from '@/pages/memories/detail'

export const meta: MetaFunction = () => {
  return [{ title: '经验' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function MainMemories() {
  useTitle('经验')
  const memories = useMemories(state => state.memories)

  useEffect(() => {
    useMemories.getState().init()
    return () => {
      useMemories.getState().reset()
    }
  }, [])

  if (memories === undefined) {
    return (
      <div className='size-full flex-center bg-#F7F7FA rd-24px'>
        <Image
          className='w-80px object-contain'
          src={`${basePath}/collection-loading.gif`}
        />
      </div>
    )
  }

  if (memories.length === 0) {
    return (
      <div className='size-full bg-#F7F7FA rd-24px'>
        <MemoriesEmpty />
      </div>
    )
  }

  return (
    <div className='size-full flex bg-#F7F7FA/60 rd-24px b-solid b-1 b-#E1E1E5/80 of-hidden'>
      <MemoryList />
      <div className='w-1px h-full bg-#E1E1E5/80'></div>
      <MemoryDetail />
    </div>
  )
}

export default !import.meta.env.SSR ? memo(MainMemories) : () => null
