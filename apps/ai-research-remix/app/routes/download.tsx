/* eslint-disable no-new */
import { memo, useEffect } from 'react'
import { json, useLoaderData, type MetaFunction } from '@remix-run/react'
import { useTitle } from 'ahooks'
import { AppleFilled, WindowsFilled } from '@ant-design/icons'
import { Image } from '@/components/base/image'
import { basePath } from '@/const'

export const meta: MetaFunction = () => {
  return [{ title: '下载' }]
}

export async function loader() {
  const platforms = ['darwin', 'win']

  const [darwinRelease, winRelease] = await Promise.all(
    platforms.map(platform => {
      const url = `https://installer-bty-ai-prod.oss-cn-hangzhou.aliyuncs.com/MindNote/${platform}/RELEASES.json`
      return fetch(url).then(response => response.json())
    }),
  )

  return json([darwinRelease, winRelease])
}

function Download() {
  const data = useLoaderData<typeof loader>()

  useTitle('下载')

  useEffect(() => {
    new window.Color4Bg.AestheticFluidBg({
      dom: 'login-bg',
      colors: [
        '#ffc2d4',
        '#66b0ff',
        '#8a9dff',
        '#ffffff',
        '#738af7',
        '#bdeeff',
      ],
      loop: true,
    })
  }, [])

  const download = (url: string) => {
    const link = document.createElement('a')
    link.href = url
    link.download = 'RELEASES.json'
    link.click()
  }

  return (
    <div className='mind-login relative w-screen h-screen flex justify-center items-center'>
      <div className='absolute inset-0px' id='login-bg'></div>
      <div className='relative z-100 flex flex-col size-full'>
        <div className='flex-center h-64px'>
          <Image className='h-32px' src={`${basePath}/logo-text.png`} />
        </div>
        <div className='flex-1 flex-center flex-col'>
          <p className='text-68px/88px font-bold font-[Alibaba-PuHuiTi]'>
            NovaTeam
          </p>
          <p className='text-56px/72px font-bold font-[Alibaba-PuHuiTi]'>
            Collect Fast, Create Smarter
          </p>
          <div className='flex-center gap-24px mt-60px'>
            <div
              className='w-240px h-60px text-20px font-500 rd-12px flex-center gap-6px bg-#fff cursor-pointer duration-100 hover:opacity-80'
              onClick={() => {
                const version = data[0]?.version as string
                if (version) {
                  const url = `https://installer.betteryeah.com/MindNote/darwin/MindNote%20${version}.dmg`
                  download(url)
                }
              }}
            >
              <AppleFilled />
              下载Mac版
            </div>

            <div
              className='w-240px h-60px text-20px font-500 rd-12px flex-center gap-6px bg-#fff cursor-pointer'
              onClick={() => {
                const version = data[1]?.version as string
                if (version) {
                  const url = `https://installer.betteryeah.com/MindNote/win/MindNote-${version}.7z`
                  download(url)
                }
              }}
            >
              <WindowsFilled />
              Windows版
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(Download) : () => null
