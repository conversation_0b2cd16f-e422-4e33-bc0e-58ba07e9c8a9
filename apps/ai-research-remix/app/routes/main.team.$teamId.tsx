import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { use<PERSON>ara<PERSON>, useNavigate } from '@remix-run/react'
import { memo, useMemo, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { useRequest } from 'ahooks'
import { Spin, Empty, Typography } from 'antd'
import type { TeamAgent, TeamToolItem } from '@apis/mindnote/team'
import { getTeamAgentToolList } from '@apis/mindnote/team'
import { InsertTool } from '@/pages/team/InsertTool'
import OptionTool from '@/pages/team/InsertTool/components/OptionTool'
import { ScrollView } from '@/components/base/scroll-view'

export const meta: MetaFunction = () => [{ title: '团队详情' }]

export async function loader({ context }: LoaderFunctionArgs) {
  return {
    user: context.user,
    token: context.userToken as string,
  }
}

const NewToolButton: React.FC<{
  agentId?: string
  flowIds?: string[]
  className?: string
  children?: React.ReactNode
  onRefresh: () => void
}> = ({
  agentId,
  flowIds,
  className = '',
  children = '添加工具',
  onRefresh,
}) => (
  <button
    onClick={e => {
      e?.stopPropagation()
      NiceModal.show(InsertTool, {
        scene: 'flow',
        agentId,
        flowIds: flowIds || [],
        onOk: () => {
          onRefresh()
        },
      })
    }}
    className={`opacity-0 group-hover:opacity-100 flex items-center gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-300 self-center ${className}`}
  >
    <span className='text-gray-700'>+</span>
    <span className='text-sm font-medium text-gray-700'>{children}</span>
  </button>
)

// 通用字体样式
const fontStyles = {
  pf: 'font-["PingFang_SC"] text-16px font-500 leading-16px tracking-0',
  pfLarge: 'font-["PingFang_SC"] text-20px font-500 leading-18px tracking-0',
  pfNormal:
    'font-["PingFang_SC"] text-14px font-normal leading-14px tracking-0',
  pfMedium: 'font-["PingFang_SC"] text-14px font-400 leading-14px tracking-0',
} as const

// 按钮状态样式
const buttonStyles = {
  base: `px-12px py-10px rounded-lg transition-all duration-200 ${fontStyles.pf}`,
  selected: 'bg-white text-[#17171D] shadow-sm',
  unselected: 'bg-gray-100 text-[#17171D]/40',
  hover: 'hover:bg-white',
} as const

const AgentCard: React.FC<{
  agent: TeamAgent
  onRefresh: () => void
}> = ({ agent, onRefresh }) => {
  const tools = agent.tools || []
  const [activeSegment, setActiveSegment] = useState<'builtin' | 'custom'>(
    'builtin',
  )

  // 分离内置工具和自定义工具
  const { builtinTools, customTools } = useMemo(() => {
    const builtin: TeamToolItem[] = []
    const custom: TeamToolItem[] = []

    tools.forEach(tool => {
      if (tool.plugin_type === 'SYSTEM_AGENT_TOOL') {
        builtin.push(tool)
      } else {
        custom.push(tool)
      }
    })

    return { builtinTools: builtin, customTools: custom }
  }, [tools])

  // 获取当前显示的工具列表
  const currentTools = activeSegment === 'builtin' ? builtinTools : customTools

  return (
    <div className='group overflow-hidden mt-[34px]'>
      {/* Agent 头部 */}
      <div className='flex items-center gap-4 transition-colors min-w-0'>
        {/* Agent 头像 */}
        <div className='w-80px h-80px rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden'>
          {agent.icon ? (
            <img
              src={agent.icon}
              alt={agent.agent_name}
              className='w-80px h-80px object-cover'
            />
          ) : (
            <div
              className='w-full h-full rounded-full flex items-center justify-center'
              style={{ backgroundColor: '#3B82F6' }}
            >
              <div className='w-8 h-8 rounded-full bg-white/20 flex items-center justify-center'>
                <div className='w-6 h-6 rounded-full bg-white/40' />
              </div>
            </div>
          )}
        </div>
        {/* Agent 信息 */}
        <div className='flex-1 min-w-0'>
          <h3 className={`${fontStyles.pfLarge} mb-12px text-[#17171D]`}>
            {`${agent.agent_name} ${agent.agent_expert_name}`}
          </h3>
          <p className={`${fontStyles.pfNormal} text-[#8D8D99]`}>
            {agent.agent_desc || ''}
          </p>
        </div>
        <NewToolButton
          agentId={agent.agent_id}
          flowIds={agent.flowIds}
          onRefresh={onRefresh}
        />
      </div>

      {/* 工具类型选择器 */}
      <div className='mt-24px mb-16px flex gap-2'>
        <button
          onClick={() => setActiveSegment('builtin')}
          className={`${buttonStyles.base} ${
            activeSegment === 'builtin'
              ? buttonStyles.selected
              : `${buttonStyles.unselected} ${buttonStyles.hover}`
          }`}
        >
          内置工具
        </button>
        <button
          onClick={() => setActiveSegment('custom')}
          className={`${buttonStyles.base} ${
            activeSegment === 'custom'
              ? buttonStyles.selected
              : `${buttonStyles.unselected} ${buttonStyles.hover}`
          }`}
        >
          自定义工具
        </button>
      </div>

      {/* 工具列表 */}
      <div className='mb-50px'>
        {currentTools.length > 0 ? (
          <div className='grid grid-cols-3 md:grid-cols-2 xl:grid-cols-4 gap-24px'>
            {currentTools.map((tool: TeamToolItem) => (
              <div
                key={tool.function_id}
                className={`bg-white p-24px rounded-20px border border-[#E1E1E5 80%] hover:shadow-sm transition-shadow ${
                  tool.plugin_type === 'SYSTEM_AGENT_TOOL'
                    ? ''
                    : 'cursor-pointer'
                }`}
                onClick={() => {
                  if (tool.plugin_type === 'SYSTEM_AGENT_TOOL') return
                  NiceModal.show(OptionTool, {
                    agentId: agent.agent_id,
                    mode: 'edit',
                    flowId: tool.config?.flow_id || '',
                    name: tool.name,
                    functionId: tool.function_id,
                    currentWorkSpaceId: tool.config?.workspace_id || '',
                    applicationInfo: {
                      ...tool,
                      applicationType: 'AI',
                      id: tool.function_id,
                      flowId: tool.config?.flow_id,
                    },
                    onOk: () => {
                      onRefresh()
                    },
                  })
                }}
              >
                <div className='flex flex-col items-start'>
                  {/* 1. icon */}
                  {tool.icon?.startsWith('http') ? (
                    <img
                      src={tool.icon}
                      alt={tool.name}
                      className='w-40px h-40px object-cover mb-12px'
                    />
                  ) : (
                    <div
                      className='w-40px h-40px p-8px rounded-12px flex items-center justify-center text-lg mb-12px'
                      style={{ backgroundColor: tool.color || '#E5E7EB' }}
                    >
                      {tool.icon || '🔧'}
                    </div>
                  )}
                  {/* 2. name */}
                  <div className={`${fontStyles.pf} text-[#17171D] mb-7px`}>
                    {tool.name}
                  </div>
                  {/* 3. plugin_type */}
                  <span
                    className={`${fontStyles.pfNormal} text-[#8D8D99] mb-12px`}
                  >
                    {`@${(() => {
                      switch (tool.plugin_type) {
                        case 'AI':
                          return '工作流'
                        case 'SYSTEM_AGENT_TOOL':
                          return '官方'
                        default:
                          return tool.plugin_type
                      }
                    })()}`}
                  </span>
                  {/* 4. description */}
                  <Typography.Text
                    className={`${fontStyles.pfMedium} text-[#3F3F44] !mb-0`}
                    ellipsis={{
                      tooltip: tool?.config?.description || tool?.description,
                    }}
                  >
                    {tool?.config?.description ||
                      tool?.description ||
                      '暂无描述'}
                  </Typography.Text>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className='text-center py-8 text-gray-500'>
            {activeSegment === 'builtin' ? '暂无内置工具' : '暂无自定义工具'}
          </div>
        )}
      </div>
    </div>
  )
}

function TeamDetail() {
  const params = useParams()
  const navigate = useNavigate()
  const teamId = params.teamId as string

  const {
    data: teamAgentToolData,
    loading,
    error,
    refresh: refreshTeamData,
  } = useRequest(
    async () => {
      if (!teamId) throw new Error('Team ID is required')
      const response = await getTeamAgentToolList(teamId)
      return response
    },
    {
      refreshDeps: [teamId],
      onError: err => {
        console.error('获取团队Agent和工具列表失败:', err)
      },
    },
  )

  const team = teamAgentToolData
    ? {
        team_id: teamAgentToolData.team_id || teamId,
        team_name: teamAgentToolData.team_name || '未知团队',
      }
    : null

  const agentList: TeamAgent[] = useMemo(
    () =>
      (teamAgentToolData?.agents || []).map(agent => ({
        ...agent,
        flowIds:
          agent.tools?.map(tool => tool.config?.flow_id).filter(Boolean) || [],
      })),
    [teamAgentToolData],
  )

  if (loading) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <Spin size='large' />
        </div>
      </div>
    )
  }

  if (error || !team) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <Empty
              description={
                <div>
                  <div className='text-xl mb-4'>
                    {error
                      ? '获取团队详情失败'
                      : `团队不存在 (TeamId: ${teamId})`}
                  </div>
                  {error && (
                    <div className='text-gray-500 mb-4'>
                      {error.message || '请检查网络连接或稍后重试'}
                    </div>
                  )}
                </div>
              }
            />
            <button
              onClick={() => navigate('/main/team')}
              className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              返回团队列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='size-full relative overflow-hidden bg-white rounded-[24px] flex flex-col'>
      <div
        className='absolute inset-0 rounded-[24px] z-0'
        style={{
          background:
            'linear-gradient(180deg, #FFFFFF 11%, #FFFFFF 11%, rgba(255, 255, 255, 0) 61%), linear-gradient(282deg, rgba(184, 218, 255, 0.7) 0%, rgba(216, 237, 254, 0.7) 38%, rgba(232, 251, 255, 0.7) 73%), #FFFFFF',
          filter: 'blur(360px)',
        }}
      />
      <div className='relative z-10 flex flex-col h-full'>
        {/* 顶部返回和团队名 */}
        <div
          style={{
            paddingLeft: 24,
            paddingTop: 18,
            marginBottom: 6,
            display: 'inline-flex',
          }}
          className='items-center cursor-pointer'
          onClick={() => navigate('/main/team')}
        >
          <div className='i-icons-arrow-left w-14px h-14px text-[#17171D]'></div>
          <span className={`${fontStyles.pfLarge} ml-8px text-[#17171D]`}>
            {team.team_name}
          </span>
        </div>
        <ScrollView>
          <div className='flex-1 px-6 md:px-12 lg:px-16 xl:px-20 overflow-y-auto'>
            <div className='space-y-6'>
              {agentList.map(agent => (
                <AgentCard
                  key={agent.agent_id}
                  agent={agent}
                  onRefresh={refreshTeamData}
                />
              ))}
            </div>
            {agentList.length === 0 && <div className='h-10'></div>}
          </div>
        </ScrollView>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(TeamDetail) : () => null
