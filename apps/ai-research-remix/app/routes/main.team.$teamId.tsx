import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { use<PERSON>ara<PERSON>, useNavigate } from '@remix-run/react'
import { memo, useMemo, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { useRequest } from 'ahooks'
import { Spin, Empty, Typography } from 'antd'
import type { TeamToolItem } from '@apis/mindnote/team'
import { getTeamAgentToolList } from '@apis/mindnote/team'
import { getAgentTeamDetail } from '@apis/mindnote/next-agent-chat'
import { InsertTool } from '@/pages/team/InsertTool'
import OptionTool from '@/pages/team/InsertTool/components/OptionTool'
import { ScrollView } from '@/components/base/scroll-view'

export const meta: MetaFunction = () => [{ title: '团队详情' }]

export async function loader({ context }: LoaderFunctionArgs) {
  return {
    user: context.user,
    token: context.userToken as string,
  }
}

// 通用字体样式
const fontStyles = {
  pf: 'font-["PingFang_SC"] text-16px font-500 leading-16px tracking-0',
  pfLarge: 'font-["PingFang_SC"] text-20px font-500 leading-18px tracking-0',
  pfNormal:
    'font-["PingFang_SC"] text-14px font-normal leading-14px tracking-0',
  pfMedium: 'font-["PingFang_SC"] text-14px font-400 leading-14px tracking-0',
} as const

// 按钮状态样式
const buttonStyles = {
  base: `px-12px py-10px rounded-lg transition-all duration-200 ${fontStyles.pf}`,
  selected: 'bg-white text-[#17171D] shadow-sm',
  unselected: 'bg-gray-100 text-[#17171D]/40',
  hover: 'hover:bg-white',
} as const

// 团队背景渐变样式
function getTeamGradient(color: string) {
  return `linear-gradient(180deg, rgba(247, 247, 250, 0) 50%, #F7F7FA 98%), linear-gradient(241deg, ${color} 7%, ${color}00 95%), #F7F7FA`
}

// 根据团队名称获取背景渐变
function getTeamBackground(teamName: string) {
  switch (teamName) {
    case 'Nova':
      return getTeamGradient('#EDD6FF')
    case 'Voc专家':
      return getTeamGradient('#FFDBED')
    case '销售专家':
      return getTeamGradient('#D6EEFF')
    default:
      return getTeamGradient('#EDD6FF') // 默认使用 Nova 背景
  }
}

const ToolList: React.FC<{
  tools: TeamToolItem[]
  onRefresh: () => void
}> = ({ tools, onRefresh }) => {
  const [activeSegment, setActiveSegment] = useState<'builtin' | 'custom'>(
    'builtin',
  )

  // 分离内置工具和自定义工具
  const { builtinTools, customTools } = useMemo(() => {
    const builtin: TeamToolItem[] = []
    const custom: TeamToolItem[] = []

    tools.forEach(tool => {
      if (tool.plugin_type === 'SYSTEM_AGENT_TOOL') {
        builtin.push(tool)
      } else {
        custom.push(tool)
      }
    })

    return { builtinTools: builtin, customTools: custom }
  }, [tools])

  // 获取当前显示的工具列表
  const currentTools = activeSegment === 'builtin' ? builtinTools : customTools

  return (
    <div className='group overflow-hidden'>
      {/* 工具类型选择器 */}
      <div className='my-24px flex gap-12px'>
        <button
          onClick={() => setActiveSegment('builtin')}
          className={`${buttonStyles.base} ${
            activeSegment === 'builtin'
              ? buttonStyles.selected
              : `${buttonStyles.unselected} ${buttonStyles.hover}`
          }`}
        >
          内置工具
        </button>
        <button
          onClick={() => setActiveSegment('custom')}
          className={`${buttonStyles.base} ${
            activeSegment === 'custom'
              ? buttonStyles.selected
              : `${buttonStyles.unselected} ${buttonStyles.hover}`
          }`}
        >
          自定义工具
        </button>
      </div>

      {/* 工具列表 */}
      <div className='mb-50px'>
        <div className='grid grid-cols-3 md:grid-cols-2 xl:grid-cols-4 gap-24px'>
          {/* 自定义工具页面显示创建工具卡片 */}
          {activeSegment === 'custom' && (
            <div
              className='group relative transition-all duration-300 hover:-translate-y-1 flex flex-col items-center cursor-pointer h-164px'
              onClick={() => {
                NiceModal.show(InsertTool, {
                  scene: 'flow',
                  agentId: '', // 新接口没有agent信息，暂时传空
                  flowIds: [],
                  onOk: () => {
                    onRefresh()
                  },
                })
              }}
            >
              {/* Card container */}
              <div className='relative w-full h-full bg-white shadow-sm hover:shadow-xl transition-shadow duration-300 overflow-hidden rounded-[20px] flex flex-col items-center justify-center p-24px border border-[#E1E1E5 80%]'>
                <div className='flex flex-col items-center justify-center w-full h-full transform-gpu'>
                  <div className='i-icons-add w-24px h-24px text-[#7B61FF] mb-20px flex-shrink-0'></div>
                  <div
                    className={`${fontStyles.pf} text-[#7B61FF] text-center flex-shrink-0`}
                  >
                    创建自定义工具
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentTools.map((tool: TeamToolItem) => (
            <div
              key={tool.function_id}
              className={`group relative transition-all duration-300 hover:-translate-y-1 flex flex-col items-start ${
                tool.plugin_type === 'SYSTEM_AGENT_TOOL' ? '' : 'cursor-pointer'
              } h-164px`}
              onClick={() => {
                if (tool.plugin_type === 'SYSTEM_AGENT_TOOL') return
                NiceModal.show(OptionTool, {
                  agentId: '', // 新接口没有agent信息，暂时传空
                  mode: 'edit',
                  flowId: tool.config?.flow_id || '',
                  name: tool.name,
                  functionId: tool.function_id,
                  currentWorkSpaceId: tool.config?.workspace_id || '',
                  applicationInfo: {
                    ...tool,
                    applicationType: 'AI',
                    id: tool.function_id,
                    flowId: tool.config?.flow_id,
                  },
                  onOk: () => {
                    onRefresh()
                  },
                })
              }}
            >
              {/* Card container */}
              <div className='relative w-full h-full bg-white shadow-sm hover:shadow-xl transition-shadow duration-300 overflow-hidden rounded-[20px] flex flex-col items-start p-24px border border-[#E1E1E5 80%]'>
                <div className='flex flex-col items-start w-full h-full transform-gpu'>
                  {/* 1. icon */}
                  {tool.icon?.startsWith('http') ? (
                    <img
                      src={tool.icon}
                      alt={tool.name}
                      className='w-40px h-40px object-cover mb-12px flex-shrink-0'
                    />
                  ) : (
                    <div
                      className='w-40px h-40px p-8px rounded-12px flex items-center justify-center text-lg mb-12px flex-shrink-0'
                      style={{ backgroundColor: tool.color || '#E5E7EB' }}
                    >
                      {tool.icon || '🔧'}
                    </div>
                  )}
                  {/* 2. name */}
                  <div
                    className={`${fontStyles.pf} text-[#17171D] mb-7px flex-shrink-0`}
                  >
                    {tool.name}
                  </div>
                  {/* 3. plugin_type */}
                  <span
                    className={`${fontStyles.pfNormal} text-[#8D8D99] mb-12px flex-shrink-0`}
                  >
                    {`@${(() => {
                      switch (tool.plugin_type) {
                        case 'AI':
                          return '工作流'
                        case 'SYSTEM_AGENT_TOOL':
                          return '官方'
                        default:
                          return tool.plugin_type
                      }
                    })()}`}
                  </span>
                  {/* 4. description */}
                  <Typography.Text
                    className={`${fontStyles.pfMedium} text-[#3F3F44] !mb-0 flex-1`}
                    ellipsis={{
                      tooltip: tool?.config?.description || tool?.description,
                    }}
                  >
                    {tool?.config?.description ||
                      tool?.description ||
                      '暂无描述'}
                  </Typography.Text>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 空状态显示 */}
        {currentTools.length === 0 && activeSegment === 'builtin' && (
          <div className='text-center py-8 text-gray-500'>暂无内置工具</div>
        )}
      </div>
    </div>
  )
}

function TeamDetail() {
  const params = useParams()
  const navigate = useNavigate()
  const teamId = params.teamId as string

  // 获取团队详情
  const {
    data: teamDetailData,
    loading: teamLoading,
    error: teamError,
    refresh: refreshTeamDetail,
  } = useRequest(
    async () => {
      if (!teamId) throw new Error('Team ID is required')
      const response = await getAgentTeamDetail(teamId)
      return response
    },
    {
      refreshDeps: [teamId],
      onError: err => {
        console.error('获取团队详情失败:', err)
      },
    },
  )

  // 获取团队工具列表
  const {
    data: toolsData,
    loading: toolsLoading,
    error: toolsError,
    refresh: refreshToolsData,
  } = useRequest(
    async () => {
      if (!teamId) throw new Error('Team ID is required')
      const response = await getTeamAgentToolList(teamId)
      return response
    },
    {
      refreshDeps: [teamId],
      onError: err => {
        console.error('获取团队工具列表失败:', err)
      },
    },
  )

  const tools: TeamToolItem[] = toolsData || []
  const loading = teamLoading || toolsLoading
  const error = teamError || toolsError

  const refreshTeamData = () => {
    refreshTeamDetail()
    refreshToolsData()
  }

  if (loading) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <Spin size='large' />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <Empty
              description={
                <div>
                  <div className='text-xl mb-4'>
                    {teamError ? '获取团队详情失败' : '获取团队工具列表失败'}
                  </div>
                  <div className='text-gray-500 mb-4'>
                    {error.message || '请检查网络连接或稍后重试'}
                  </div>
                </div>
              }
            />
            <button
              onClick={() => navigate('/main/team')}
              className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              返回团队列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='size-full relative overflow-hidden bg-white rounded-[24px] flex flex-col'>
      <div
        className='absolute inset-0 rounded-[24px] z-0'
        style={{
          background: getTeamBackground(teamDetailData?.team_name || 'Nova'),
          filter: 'blur(60px)',
        }}
      />
      <div className='relative z-10 flex flex-col h-full'>
        {/* 顶部返回和团队名 */}
        <div
          style={{
            paddingLeft: 24,
            paddingTop: 18,
            marginBottom: 6,
            display: 'inline-flex',
          }}
          className='items-center cursor-pointer'
          onClick={() => navigate('/main/team')}
        >
          <div className='i-icons-arrow-left w-14px h-14px text-[#17171D]'></div>
          <span className={`${fontStyles.pfLarge} ml-8px text-[#17171D]`}>
            {teamDetailData?.team_name || '团队工具'}
          </span>
        </div>
        <ScrollView>
          <div className='flex-1 px-6 md:px-12 lg:px-16 xl:px-20 overflow-y-auto'>
            {/* Agent 头部 */}
            <div className='flex items-center transition-colors min-w-0 mt-24px'>
              {/* Agent 头像 */}
              <div className='w-80px h-80px rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden'>
                {teamDetailData?.icon ? (
                  <img
                    src={teamDetailData.icon}
                    alt={teamDetailData.team_name}
                    className='w-80px h-80px object-cover'
                  />
                ) : (
                  <div
                    className='w-full h-full rounded-full flex items-center justify-center'
                    style={{ backgroundColor: '#3B82F6' }}
                  >
                    <div className='w-8 h-8 rounded-full bg-white/20 flex items-center justify-center'>
                      <div className='w-6 h-6 rounded-full bg-white/40' />
                    </div>
                  </div>
                )}
              </div>
              {/* Agent 信息 */}
              <div className='flex-1 min-w-0 ml-12px'>
                <h3 className={`${fontStyles.pfLarge} mb-12px text-[#17171D]`}>
                  {`${teamDetailData?.team_name || 'unknown'} ${teamDetailData?.team_desc || ''}`}
                </h3>
                <p className={`${fontStyles.pfNormal} text-[#8D8D99]`}>
                  {teamDetailData?.team_desc || '暂无描述'}
                </p>
              </div>
            </div>

            <div className='space-y-6'>
              <ToolList tools={tools} onRefresh={refreshTeamData} />
            </div>
            {tools.length === 0 && <div className='h-10'></div>}
          </div>
        </ScrollView>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(TeamDetail) : () => null
