import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { Outlet } from '@remix-run/react'
import { useTitle } from 'ahooks'
import { memo } from 'react'

export const meta: MetaFunction = () => {
  return [{ title: '团队' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return {
    user: context.user,
    token: context.userToken as string,
  }
}

// Parent layout component for team pages
function TeamLayout() {
  useTitle('团队')

  return <Outlet />
}

export default !import.meta.env.SSR ? memo(TeamLayout) : () => null
