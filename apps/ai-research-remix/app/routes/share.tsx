import type { MetaFunction } from '@remix-run/react'
import { Outlet, useMatch } from '@remix-run/react'
import { memo } from 'react'

export const meta: MetaFunction = () => {
  return [{ title: 'NovaTeam' }]
}

function Share() {
  const shareMatch = useMatch('/share/:type/*')
  if (shareMatch) {
    return (
      <main className='size-full flex of-hidden bg-#F3F3F5'>
        <Outlet />
      </main>
    )
  }
}

export default !import.meta.env.SSR ? memo(Share) : () => null
