import path from 'node:path'
import express from 'express'
import { fromMarkdown } from 'mdast-util-from-markdown'
import PdfPrinter from 'pdfmake'
import type { Root, RootContent } from 'mdast'
import { gfmTable } from 'micromark-extension-gfm-table'
import { gfmTableFromMarkdown } from 'mdast-util-gfm-table'
import { getResourceURI } from '../utils/resource'

// const Pptxgen = pptxgen.default

export const fileConvertRouter = express.Router()

fileConvertRouter.post('/to_pdf', async (req, res) => {
  const { filepath, taskId } = req.body as { filepath: string; taskId: string }

  if (path.extname(filepath) !== '.md') {
    return res.status(400).json({
      error: 'Only markdown files are supported',
    })
  }

  try {
    const response = await getResourceURI(filepath, taskId)
    const markdown = await response.text()

    const fonts = {
      AlibabaPuHuiTi: {
        normal: path.join(
          process.cwd(),
          'public/fonts/AlibabaPuHuiTi-Regular.woff2',
        ),
        bold: path.join(
          process.cwd(),
          'public/fonts/AlibabaPuHuiTi-Bold.woff2',
        ),
        italics: path.join(
          process.cwd(),
          'public/fonts/AlibabaPuHuiTi-Regular.woff2',
        ),
        bolditalics: path.join(
          process.cwd(),
          'public/fonts/AlibabaPuHuiTi-Bold.woff2',
        ),
      },
    }

    const tree = fromMarkdown(markdown, {
      extensions: [gfmTable()],
      mdastExtensions: [gfmTableFromMarkdown()],
    })

    const docDefinition = convertMarkdownToPdfMake(tree)

    const printer = new PdfPrinter(fonts)

    const pdfDoc = printer.createPdfKitDocument(docDefinition)

    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', 'attachment; filename="export.pdf"')

    pdfDoc.pipe(res)
    pdfDoc.end()
  } catch (error) {
    res
      .status(500)
      .json({ error: error instanceof Error ? error.message : String(error) })
  }
})

function convertMarkdownToPdfMake(tree: Root): any {
  // 递归处理节点
  function processNode(node: RootContent): any {
    if (!node) return null

    switch (node.type) {
      case 'heading': {
        const text = processInlineNodes(node.children)
        return {
          text,
          style: `header${node.depth}`,
          margin: [0, 8, 0, 4], // [0, 10, 0, 5] * 0.8
        }
      }

      case 'paragraph': {
        const text = processInlineNodes(node.children)
        return {
          text,
          margin: [0, 0, 0, 6.4], // [0, 0, 0, 8] * 0.8
        }
      }

      case 'text': {
        return node.value || ''
      }

      case 'strong': {
        const text = processInlineNodes(node.children || [])
        return { text, style: 'strong' }
      }

      case 'emphasis': {
        const text = processInlineNodes(node.children || [])
        return {
          text,
          italics: true,
        }
      }

      case 'inlineCode': {
        return {
          text: node.value || '',
          background: '#f5f5f5',
          fontSize: 6, // 7.5 * 0.8
        }
      }

      case 'link': {
        const text = processInlineNodes(node.children)
        return {
          text,
          link: node.url,
          color: '#0969da',
        }
      }

      case 'list': {
        return {
          [node.ordered ? 'ol' : 'ul']: node.children.map(child => {
            const text = processInlineNodes(child.children)
            if (Array.isArray(text)) {
              if (text.length === 1) {
                return { text: text[0] }
              }
              return text.map(t => {
                if ('ol' in t || 'ul' in t) {
                  return t
                }
                return { text: t }
              })
            }
            return { text }
          }),
          margin: [0, 0, 0, 6.4], // [0, 0, 0, 8] * 0.8
        }
      }

      case 'blockquote': {
        const content = []
        if (node.children) {
          for (const child of node.children) {
            const processed = processNode(child)
            if (processed) {
              content.push(processed)
            }
          }
        }
        return {
          stack: content,
          margin: [16, 0, 0, 6.4], // [20, 0, 0, 8] * 0.8
          italics: true,
        }
      }

      case 'code': {
        return {
          text: node.value || '',
          background: '#f5f5f5',
          margin: [0, 4, 0, 6.4], // [0, 5, 0, 8] * 0.8
          fontSize: 6, // 7.5 * 0.8
          preserveLeadingSpaces: true,
        }
      }

      case 'thematicBreak': {
        return {
          canvas: [
            {
              type: 'line',
              x1: 0,
              y1: 5,
              x2: 595 - 2 * 40,
              y2: 5,
              lineWidth: 1,
              lineColor: '#d1d9e0',
            },
          ],
          margin: [0, 19.2, 0, 19.2], // [0, 24, 0, 24] * 0.8
        }
      }

      case 'table': {
        const body = node.children
          .filter(row => row.type === 'tableRow')
          .map((row, rowIndex) =>
            row.children
              .filter(cell => cell.type === 'tableCell')
              .map(cell => {
                const text = processInlineNodes(cell.children)
                return rowIndex === 0
                  ? { text, style: 'tableHeader' }
                  : { text, style: 'tableCell' }
              }),
          )

        return {
          table: {
            headerRows: 1,
            body,
            widths: Array(body[0]?.length || 0).fill('*'),
          },
          layout: {
            fillColor: (rowIndex: number) =>
              (rowIndex - 1) % 2 === 0 ? null : '#f5f5f5',
            hLineWidth: (i: number, node: any) => {
              // 只在最上方和最下方绘制水平线
              return i === 0 || i === node.table.body.length ? 1 : 0
            },
            vLineWidth: (i: number, node: any) => {
              // 只在最左边和最右边绘制垂直线
              return i === 0 || i === node.table.widths.length ? 1 : 0
            },
            hLineColor: '#eaeaea',
            vLineColor: '#eaeaea',
          },
          margin: [0, 4, 0, 6.4], // [0, 5, 0, 8] * 0.8
        }
      }

      default:
        return null
    }
  }

  function processInlineNodes(children: RootContent[]) {
    const result = children.map(node => processInlineNode(node))
    if (result.some(el => typeof el !== 'string')) {
      return result
    }
    return result.join('')
  }

  // 处理单个内联节点
  function processInlineNode(node: RootContent): any {
    switch (node.type) {
      case 'paragraph': {
        return processInlineNodes(node.children)
      }

      case 'text':
        return node.value || ''

      case 'strong': {
        const text = processInlineNodes(node.children)
        return { text, style: 'strong' }
      }

      case 'link': {
        const text = processInlineNodes(node.children)
        return {
          text,
          link: node.url,
          color: '#0969da',
        }
      }

      case 'list': {
        const list = {
          [node.ordered ? 'ol' : 'ul']: node.children.map(child => {
            const text = processInlineNodes(child.children)
            return { text: Array.isArray(text) ? text[0] : text }
          }),
          margin: [0, 0, 0, 6.4], // [0, 0, 0, 8] * 0.8
        }
        return list
      }

      case 'emphasis': {
        const text = processInlineNodes(node.children || [])
        if (typeof text === 'string') {
          return { text, italics: true }
        }
        return { text: [text], italics: true }
      }

      case 'break': {
        return '\n'
      }

      case 'inlineCode':
        return {
          text: node.value || '',
          background: '#f5f5f5',
          fontSize: 6, // 7.5 * 0.8
        }

      default:
        if ('children' in node && node.children) {
          return processInlineNodes(node.children)
        }
        return null
    }
  }

  const content = []
  for (const child of tree.children) {
    const processed = processNode(child)
    if (processed) {
      content.push(processed)
    }
  }

  return {
    content,
    defaultStyle: {
      color: '#17171d',
      font: 'AlibabaPuHuiTi',
      fontSize: 9.6, // 12 * 0.8
      lineHeight: 1.5,
    },
    styles: {
      header1: {
        fontSize: 17.28, // 21.6 * 0.8
        bold: true,
      },
      header2: {
        fontSize: 14.4, // 18 * 0.8
        bold: true,
      },
      header3: {
        fontSize: 12, // 15 * 0.8
        bold: true,
      },
      header4: {
        bold: true,
      },
      header5: {
        bold: true,
      },
      header6: {
        bold: true,
      },
      strong: {
        bold: true,
      },
      tableHeader: {
        fontSize: 8.4, // 10.5 * 0.8
        bold: true,
      },
      tableCell: {
        fontSize: 8.4, // 10.5 * 0.8
        color: '#666666',
      },
    },
    pageSize: 'A4',
    pageMargins: 40,
  }
}

// fileConvertRouter.post('/to_pptx', async (req, res) => {
//   //   const html = `
//   // <!DOCTYPE html>
//   // <html lang="zh">
//   //   <head>
//   //     <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
//   //     <script src="https://d3js.org/d3.v7.min.js"></script>
//   //     <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
//   //     <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
//   //     <style>
//   //       @font-face {
//   //         font-family: 'Source Han Sans CN';
//   //         src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
//   //       }
//   //       body {
//   //         font-family: 'Source Han Sans CN', 'Noto Sans SC', sans-serif;
//   //         margin: 0;
//   //         padding: 0;
//   //       }
//   //       .slide-container {
//   //         width: 1280px;
//   //         min-height: 720px;
//   //         background: linear-gradient(135deg, #1E88E5 0%, #43A047 100%);
//   //         color: #FFFFFF;
//   //         display: flex;
//   //         flex-direction: column;
//   //         justify-content: center;
//   //         align-items: center;
//   //         padding: 40px;
//   //       }
//   //       .title {
//   //         font-size: 48px;
//   //         font-weight: 700;
//   //         margin-bottom: 30px;
//   //         text-align: center;
//   //         line-height: 1.3;
//   //       }
//   //       .subtitle {
//   //         font-size: 24px;
//   //         margin-bottom: 60px;
//   //         text-align: center;
//   //       }
//   //       .date {
//   //         font-size: 20px;
//   //         margin-top: 40px;
//   //       }
//   //     </style>
//   //   </head>
//   //   <body>
//   //     <div class="slide-container">
//   //       <div class="content-wrapper">
//   //         <img src="https://private-us-east-1.manuscdn.com/sessionFile/VSouedY3GTFmI0gun5XpO7/sandbox/slides_resource_il2473rkd0c9nxavkujdx-3839b14f-e2b-prod-aws_1752677086951_na1fn_L2hvbWUvdWJ1bnR1L3VwbG9hZC9zZWFyY2hfaW1hZ2VzL3RnTnBUQ1EzNTR2Sg.jpg?x-oss-process=image/resize,w_1560,h_1560/format,webp&Expires=1798761600&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9wcml2YXRlLXVzLWVhc3QtMS5tYW51c2Nkbi5jb20vc2Vzc2lvbkZpbGUvVlNvdWVkWTNHVEZtSTBndW41WHBPNy9zYW5kYm94L3NsaWRlc19yZXNvdXJjZV9pbDI0NzNya2QwYzlueGF2a3VqZHgtMzgzOWIxNGYtZTJiLXByb2QtYXdzXzE3NTI2NzcwODY5NTFfbmExZm5fTDJodmJXVXZkV0oxYm5SMUwzVndiRzloWkM5elpXRnlZMmhmYVcxaFoyVnpMM1JuVG5CVVExRXpOVFIyU2cuanBnP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLHdfMTU2MCxoXzE1NjAvZm9ybWF0LHdlYnAiLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE3OTg3NjE2MDB9fX1dfQ__&Key-Pair-Id=K2HSFNDJXOU9YS&Signature=Rym8JAF4xn0w5t5kTU1ugWOnM3QS2wc2bs6fhbUi2mY4h4ZRhobUVPymcmnlmaDK7P2EOKTKLEKEL49-z2cwP-OSup4fl1N1cP9ZTEzWir-WeFQLUxLtHcZVaFJfgzs7equThz5kipxAXTXUI7Rz-pa67BVhG12228~jBQ5g0wxajrZTnB-Py1lPdX-AZfChfpCE6YAWeoXfg~Yt74gxgtljSyh6DFBSeuPMqgCmdCSaVFtQwrjnU7VuREaZjSqjF4D89sUgOCYvx6FJa50tDMTFDgEXvsmjEGiE3gi6~hnR2T1Sirr2xwbMxBXaFk~48Eq1MXDwJ1zrX0dqbLyy-Q__" alt="光伏产业" style="width: 600px; height: 300px; object-fit: cover; border-radius: 10px; margin-bottom: 40px;">
//   //         <h1 class="title">A股光伏板块近期走势与政策影响分析</h1>
//   //         <p class="subtitle">专业解读行业动态与未来发展趋势</p>
//   //         <p class="date">2025年7月16日</p>
//   //       </div>
//   //     </div>
//   //   </body>
//   // </html>
//   // `
//   // const dom = new JSDOM(html)
//   // console.log(dom)
//   // const pptx = new Pptxgen()
//   // // 2. Add a Slide to the presentation
//   // const slide = pptx.addSlide()
//   // // 3. Add 1+ objects (Tables, Shapes, etc.) to the Slide
//   // const textboxText = 'Hello World from PptxGenJS!'
//   // const textboxOpts = { x: 1, y: 1, color: '363636' }
//   // slide.addText(textboxText, textboxOpts)
//   // const data = await pptx.stream()
//   // // 处理不同类型的数据以获取正确的长度
//   // let buffer: Buffer
//   // let contentLength: number
//   // if (data instanceof ArrayBuffer) {
//   //   console.log('====== ArrayBuffer')
//   //   buffer = Buffer.from(data)
//   //   contentLength = data.byteLength
//   // } else if (data instanceof Uint8Array) {
//   //   console.log('====== Uint8Array')
//   //   buffer = Buffer.from(data)
//   //   contentLength = data.length
//   // } else if (data instanceof Blob) {
//   //   console.log('====== Blob')
//   //   const arrayBuffer = await data.arrayBuffer()
//   //   buffer = Buffer.from(arrayBuffer)
//   //   contentLength = data.size
//   // } else if (typeof data === 'string') {
//   //   console.log('====== string')
//   //   buffer = Buffer.from(data, 'binary')
//   //   contentLength = buffer.length
//   // } else {
//   //   // 默认处理
//   //   buffer = Buffer.from(data as any, 'binary')
//   //   contentLength = buffer.length
//   // }
//   // res.writeHead(200, {
//   //   'Content-Type':
//   //     'application/vnd.openxmlformats-officedocument.presentationml.presentation',
//   //   'Content-disposition': 'attachment;filename=demo.pptx',
//   //   'Content-Length': contentLength,
//   // })
//   // res.end(buffer)
// })
