{"name": "ai-research-remix", "type": "module", "private": true, "sideEffects": true, "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "NODE_ENV=development tsx ./server/index.ts --watch ./server/**/*.ts", "build:client": "remix vite:build", "build:server": "tsx ./script/build.server.ts", "build": "npm run build:client && npm run build:server", "build:dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144 --max-semi-space-size=128\" NODE_ENV=development NODE_SSR=true npm run build", "build:pre": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144 --max-semi-space-size=128\" NODE_ENV=pre NODE_SSR=true npm run build", "build:prod": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144 --max-semi-space-size=128\" NODE_ENV=production NODE_SSR=true npm run build", "start": "NODE_ENV=production node ./build/express/index.mjs"}, "dependencies": {"@grpc/grpc-js": "^1.10.6", "@modelcontextprotocol/sdk": "^1.10.2", "@opentelemetry/api": "^1.8.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.50.0", "@opentelemetry/instrumentation": "^0.50.0", "@opentelemetry/instrumentation-express": "^0.46.0", "@opentelemetry/instrumentation-http": "^0.50.0", "@opentelemetry/resources": "^1.23.0", "@opentelemetry/sdk-trace-base": "^1.23.0", "@opentelemetry/sdk-trace-node": "^1.23.0", "@opentelemetry/semantic-conventions": "^1.23.0", "@remix-run/express": "^2.15.0", "@remix-run/node": "^2.15.0", "@remix-run/react": "^2.15.0", "@remix-run/serve": "^2.15.0", "allotment": "^1.20.4", "antd": "^5.22.3", "archiver": "^7.0.1", "body-parser": "^1.20.3", "cheerio": "^1.1.0", "classnames": "^2.3.2", "compression": "^1.7.4", "cookie": "^1.0.1", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "express": "^4.18.2", "jsdom": "^26.1.0", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "mdast-util-from-markdown": "2.0.1", "mdast-util-gfm-table": "^2.0.0", "micromark-extension-gfm-table": "^2.1.1", "pdfmake": "^0.2.20", "pptxgenjs": "^4.0.1", "qs": "^6.14.0", "react": "18.2.0", "react-dom": "18.2.0", "unplugin": "^2.2.0"}, "optionalDependencies": {"@bty/async-loader": "workspace:*", "@bty/business-components": "workspace:^", "@bty/chat": "workspace:^", "@bty/components": "workspace:^", "@bty/global-types": "workspace:^", "@bty/hooks": "workspace:^", "@bty/http-client": "workspace:^", "@bty/localize": "workspace:^", "@bty/monitor": "workspace:^", "@bty/react-auth": "workspace:^", "@bty/sms": "workspace:^", "@bty/uno": "workspace:", "@bty/unplugins": "workspace:", "@bty/uploader": "workspace:^", "@bty/util": "workspace:^"}, "devDependencies": {"@ant-design/icons": "^4.8.0", "@ebay/nice-modal-react": "^1.2.13", "@microsoft/clarity": "^1.0.0", "@remix-run/dev": "^2.15.0", "@tanstack/react-ranger": "^0.0.4", "@tiptap/extension-bullet-list": "^2.25.0", "@tiptap/extension-document": "^2.25.0", "@tiptap/extension-heading": "^2.25.0", "@tiptap/extension-list-item": "^2.25.0", "@tiptap/extension-paragraph": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/extension-task-item": "^2.25.0", "@tiptap/extension-task-list": "^2.25.0", "@tiptap/extension-text": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@types/archiver": "^6.0.3", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.17", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/mdast": "^4.0.4", "@types/pdfmake": "^0.2.11", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@unocss/reset": "catalog:", "ahooks": "^3.7.8", "click-to-react-component": "^1.1.2", "copy-to-clipboard": "^3.3.3", "embla-carousel-react": "^8.6.0", "mime": "^4.0.4", "pretty-bytes": "^6.1.1", "rc-upload": "^4.3.4", "react-countup": "^6.5.0", "react-textarea-autosize": "^8.5.3", "react-vnc": "^3.1.0", "sass": "^1.77.5", "tsx": "^3.14.0", "typescript": "^5.1.6", "unocss": "catalog:", "urllib": "^4.4.0", "uuid": "^9.0.0", "vite": "^5.1.0", "vite-plugin-commonjs": "^0.10.4", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.2.1", "xgplayer": "^3.0.17", "zustand": "^4.5.2"}}