// 基于业务脑图的操作日志映射表配置

// 模块定义 - 根据脑图中的实际业务模块
export const MODULES = {
  // 用户相关
  USER_AUTH: 'user_auth', // 用户模块

  // 订单相关
  ORDER_CENTER: 'order_center', // 订单中心
  ORDER_ENTERPRISE: 'order_enterprise', // 配置企业版
  ORDER_ADDON_PACKAGE: 'order_addon_package', // 配置加量包
  ORDER_VERSION_RENEWAL: 'order_version_renewal', // 版本续约
  ORDER_DATA_EXPORT: 'order_data_export', // 订单数据导出

  // 运营后台模块
  ACCOUNT_MANAGEMENT: 'account_management', // 账号管理
  WORKSPACE_MANAGEMENT: 'workspace_management', // 空间管理
  WEBSITE_RESERVATION: 'website_reservation', // 官网预约
  FEEDBACK_SUGGESTIONS: 'feedback_suggestions', // 反馈建议
  BLOG_MANAGEMENT: 'blog_management', // 博客管理
  MINDNOTE_BETA: 'mindnote_beta', // MindNote内测
  BACKEND_ADMIN: 'backend_admin', // 后台管理
} as const

// 操作类型定义 - 根据脑图中的实际操作
export const OPERATION_TYPES = {
  // 用户操作
  LOGIN: 'login', // 登录
  LOGOUT: 'logout', // 登出

  // 订单流程操作
  SUBMIT_CERTIFICATE: 'submit_certificate', // 提交凭证
  AUDIT: 'audit', // 审核
  APPROVE: 'approve', // 通过
  REJECT: 'reject', // 驳回
  EXPORT_DATA: 'export_data', // 数据导出

  // 账号管理操作
  EXPORT: 'export', // 导出

  // 空间管理操作
  MODIFY_VERSION_LIMIT: 'modify_version_limit', // 修改版本限制
  ADD_ENTERPRISE_NAME: 'add_enterprise_name', // 添加企业名称

  // 官网和反馈操作
  SET_REMARK: 'set_remark', // 设置备注

  // MindNote内测操作
  GRANT_QUALIFICATION: 'grant_qualification', // 通过资格
  RE_GRANT_QUALIFICATION: 're_grant_qualification', // 重新通过资格
  DENY_QUALIFICATION: 'deny_qualification', // 拒绝资格

  // 后台管理操作
  EDIT_PERMISSION: 'edit_permission', // 编辑权限
  DELETE_USER: 'delete_user', // 删除用户
  CREATE_USER: 'create_user', // 添加账号

  // 通用操作
  VIEW: 'view', // 查看
  QUERY: 'query', // 查询
  CREATE: 'create', // 新增
  UPDATE: 'update', // 修改
  DELETE: 'delete', // 删除
} as const

// 操作映射表 - 定义每个模块允许的操作类型
export const OPERATION_MAP = {
  // 用户模块
  [MODULES.USER_AUTH]: [
    OPERATION_TYPES.LOGIN,
    OPERATION_TYPES.LOGOUT,
    OPERATION_TYPES.VIEW,
  ],

  // 配置企业版订单
  [MODULES.ORDER_ENTERPRISE]: [
    OPERATION_TYPES.SUBMIT_CERTIFICATE,
    OPERATION_TYPES.AUDIT,
    OPERATION_TYPES.APPROVE,
    OPERATION_TYPES.REJECT,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 配置加量包订单
  [MODULES.ORDER_ADDON_PACKAGE]: [
    OPERATION_TYPES.SUBMIT_CERTIFICATE,
    OPERATION_TYPES.AUDIT,
    OPERATION_TYPES.APPROVE,
    OPERATION_TYPES.REJECT,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 版本续约订单
  [MODULES.ORDER_VERSION_RENEWAL]: [
    OPERATION_TYPES.SUBMIT_CERTIFICATE,
    OPERATION_TYPES.AUDIT,
    OPERATION_TYPES.APPROVE,
    OPERATION_TYPES.REJECT,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 订单数据导出
  [MODULES.ORDER_DATA_EXPORT]: [
    OPERATION_TYPES.EXPORT_DATA,
    OPERATION_TYPES.VIEW,
  ],

  // 账号管理
  [MODULES.ACCOUNT_MANAGEMENT]: [
    OPERATION_TYPES.EXPORT,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 空间管理
  [MODULES.WORKSPACE_MANAGEMENT]: [
    OPERATION_TYPES.MODIFY_VERSION_LIMIT,
    OPERATION_TYPES.ADD_ENTERPRISE_NAME,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 官网预约
  [MODULES.WEBSITE_RESERVATION]: [
    OPERATION_TYPES.SET_REMARK,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 反馈建议
  [MODULES.FEEDBACK_SUGGESTIONS]: [
    OPERATION_TYPES.SET_REMARK,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 博客管理
  [MODULES.BLOG_MANAGEMENT]: [
    OPERATION_TYPES.CREATE,
    OPERATION_TYPES.UPDATE,
    OPERATION_TYPES.DELETE,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // MindNote内测
  [MODULES.MINDNOTE_BETA]: [
    OPERATION_TYPES.GRANT_QUALIFICATION,
    OPERATION_TYPES.RE_GRANT_QUALIFICATION,
    OPERATION_TYPES.DENY_QUALIFICATION,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
  ],

  // 后台管理
  [MODULES.BACKEND_ADMIN]: [
    OPERATION_TYPES.EDIT_PERMISSION,
    OPERATION_TYPES.DELETE_USER,
    OPERATION_TYPES.VIEW,
    OPERATION_TYPES.QUERY,
    OPERATION_TYPES.CREATE_USER,
  ],
} as const

// 操作中文名称映射
export const OPERATION_NAMES = {
  // 用户操作
  [OPERATION_TYPES.LOGIN]: '登录',
  [OPERATION_TYPES.LOGOUT]: '登出',

  // 订单流程操作
  [OPERATION_TYPES.SUBMIT_CERTIFICATE]: '提交凭证',
  [OPERATION_TYPES.AUDIT]: '审核',
  [OPERATION_TYPES.APPROVE]: '通过',
  [OPERATION_TYPES.REJECT]: '驳回',
  [OPERATION_TYPES.EXPORT_DATA]: '数据导出',

  // 账号管理操作
  [OPERATION_TYPES.EXPORT]: '导出',

  // 空间管理操作
  [OPERATION_TYPES.MODIFY_VERSION_LIMIT]: '修改版本限制',
  [OPERATION_TYPES.ADD_ENTERPRISE_NAME]: '添加企业名称',

  // 官网和反馈操作
  [OPERATION_TYPES.SET_REMARK]: '设置备注',

  // 博客操作
  [OPERATION_TYPES.CREATE]: '新增',
  [OPERATION_TYPES.UPDATE]: '修改',
  [OPERATION_TYPES.DELETE]: '删除',

  // MindNote内测操作
  [OPERATION_TYPES.GRANT_QUALIFICATION]: '通过资格',
  [OPERATION_TYPES.RE_GRANT_QUALIFICATION]: '重新通过资格',
  [OPERATION_TYPES.DENY_QUALIFICATION]: '拒绝资格',

  // 后台管理操作
  [OPERATION_TYPES.EDIT_PERMISSION]: '编辑权限',
  [OPERATION_TYPES.DELETE_USER]: '删除用户',
  [OPERATION_TYPES.CREATE_USER]: '添加账号',

  // 通用操作
  [OPERATION_TYPES.VIEW]: '查看',
  [OPERATION_TYPES.QUERY]: '查询',
} as const

// 模块中文名称映射
export const MODULE_NAMES = {
  [MODULES.USER_AUTH]: '用户模块',
  [MODULES.ORDER_CENTER]: '订单中心',
  [MODULES.ORDER_ENTERPRISE]: '配置企业版',
  [MODULES.ORDER_ADDON_PACKAGE]: '配置加量包',
  [MODULES.ORDER_VERSION_RENEWAL]: '版本续约',
  [MODULES.ORDER_DATA_EXPORT]: '订单数据导出',
  [MODULES.ACCOUNT_MANAGEMENT]: '账号管理',
  [MODULES.WORKSPACE_MANAGEMENT]: '空间管理',
  [MODULES.WEBSITE_RESERVATION]: '官网预约',
  [MODULES.FEEDBACK_SUGGESTIONS]: '反馈建议',
  [MODULES.BLOG_MANAGEMENT]: '博客管理',
  [MODULES.MINDNOTE_BETA]: 'MindNote内测',
  [MODULES.BACKEND_ADMIN]: '后台管理',
} as const

// 工具函数：获取模块允许的操作类型
export function getModuleOperations(module: string): string[] {
  return OPERATION_MAP[module] || []
}

// 工具函数：验证操作是否合法
export function validateOperation(
  module: string,
  operationType: string,
): boolean {
  const allowedOperations = getModuleOperations(module)
  return allowedOperations.includes(operationType)
}

// 工具函数：获取操作中文名称
export function getOperationName(operationType: string): string {
  return OPERATION_NAMES[operationType] || operationType
}

// 工具函数：获取模块中文名称
export function getModuleName(module: string): string {
  return MODULE_NAMES[module] || module
}

// 类型定义
export type ModuleType = keyof typeof MODULES
export type OperationType = keyof typeof OPERATION_TYPES

// 操作日志参数
export interface OperationLogParams {
  ip_address: string
  module: string
  new_data?: string
  old_data?: string
  operation_type: string
  operator_id: number
  operator_name: string
  user_agent: string
  record_id?: string
}

// 订单审核流程的状态定义
export const ORDER_AUDIT_STATUS = {
  PENDING: 'pending', // 待审核
  APPROVED: 'approved', // 已通过
  REJECTED: 'rejected', // 已驳回
} as const

// MindNote内测资格状态
export const MINDNOTE_QUALIFICATION_STATUS = {
  GRANTED: 'granted', // 已通过
  DENIED: 'denied', // 已拒绝
  RE_GRANTED: 're_granted', // 重新通过
} as const
