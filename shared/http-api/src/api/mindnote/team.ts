import { subscribeRequest } from '../../client'

// 团队列表项接口
export interface TeamListItem {
  team_name: string
  team_desc: string | null
  icon: string | null
  background: string | null
  creator_name: string
  updater_name: string | null
  created_at: string
  is_deleted: boolean
  team_type: string
  team_id: string
  created_by: number
  updated_by: number | null
  id: number
  updated_at: string
}

// 团队Agent接口
export interface TeamAgent {
  agent_id: string
  agent_expert_name: string
  icon: string | null
  agent_name: string
  agent_desc: string
  agent_type: string
  tools: TeamToolItem[]
  flowIds: string[]
}

// 团队工具项
export interface TeamToolItem {
  function_id: string
  name: string
  description: string | null
  icon: string
  color: string
  source: string
  plugin_type: string
  config: ToolConfig
}

export interface ToolConfig {
  inputs: ToolConfigInput[]
  flow_id: string
  description: string | null
  workspace_id: string
}

export interface ToolConfigInput {
  key: string
  name: string
  required: boolean
  field_type: string
  description: string
}

export interface TeamToolDetail {
  config: {
    description: string
    inputs: {
      key: string
      description: string
      required: boolean
      field_type: string
      name: string
    }[]
  }
}
// API 函数

// 获取用户团队列表
export function getUserTeamList() {
  return subscribeRequest.post<TeamListItem[]>('/v1/super_agent/user_team_list')
}

// 获取团队工具列表 (v2接口)
export function getTeamAgentToolList(teamId: string) {
  return subscribeRequest.post<TeamToolItem[]>(
    '/v1/completion_function/v2/agent_list/tool_list',
    {
      team_id: teamId,
    },
  )
}

/** 获取团队工具列表 */
export function getToolList(teamId: string) {
  return subscribeRequest.get<TeamListItem[]>(
    '/v1/completion_function/tool_list',
    {
      query: {
        source: teamId,
        plugin_type: 'workflow',
      },
    },
  )
}

/** 获取工具详情 */
export function getToolDetail(functionId: string) {
  return subscribeRequest.get<TeamListItem[]>(
    `/v1/completion_function/detail/tool/${functionId}`,
  )
}

/** 保存工具到团队 */
export function saveToolToTeam(params: {
  category: string
  name: string
  agentId: string
  pluginType: string
  source: string
  config: Record<string, any>
  flowId: string
  workspaceId?: string
}) {
  return subscribeRequest.post<TeamListItem[]>('/v1/completion_function/tool', {
    name: params.name,
    agent_id: params.agentId,
    source: params.source,
    plugin_type: params.pluginType,
    config: {
      ...params.config,
      flow_id: params.flowId,
      workspace_id: params.workspaceId,
    },
    external_source_id: params.flowId,
  })
}

/** 更新工具到团队 */
export function updateToolToTeam(params: {
  functionId: string
  category: string
  agentId: string
  source: string
  pluginType: string
  name: string
  flowId: string
  workspaceId?: string
  config: {
    description: string
    inputs: {
      key: string
      description: string
      required: boolean
      field_type: string
      name: string
    }[]
  }
}) {
  return subscribeRequest.put<TeamListItem[]>(
    `/v1/completion_function/${params.functionId}`,
    {
      name: params.name,
      agent_id: params.agentId,
      source: params.source,
      plugin_type: params.pluginType,
      config: {
        ...params.config,
        flow_id: params.flowId,
        workspace_id: params.workspaceId,
      },
      external_source_id: params.flowId,
    },
  )
}

/** 根据flowId获取工具详情 */
export function getToolDetailByFlowId(flowId: string) {
  return subscribeRequest.get<TeamToolDetail>(
    `/v1/completion_function/detail/tool/${flowId}`,
  )
}
